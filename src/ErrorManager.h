#ifndef ERRORMANAGER_H
#define ERRORMANAGER_H

#include <QObject>
#include <QVector>
#include <QString>
#include <QDateTime>

struct ErrorEntry {
    QString error;
    QString code;
    QDateTime timestamp;
};

class ErrorManager : public QObject {
    Q_OBJECT
public:
    static ErrorManager* instance();

    const QVector<ErrorEntry>& errors() const;
    void addError(const QString& error, const QString& code);
    void clearErrors();
    void resetErrors();

    // Load errors from storage/errorlog.json
    Q_INVOKABLE void reloadErrorsFromFile();
    // Optionally, save errors to file
    Q_INVOKABLE void saveErrorsToFile();

signals:
    void errorsChanged();

private:
    explicit ErrorManager(QObject* parent = nullptr);
    void loadErrorsFromFile();
    QVector<ErrorEntry> m_errors;
    static ErrorManager* s_instance;
};

#endif // ERRORMANAGER_H
