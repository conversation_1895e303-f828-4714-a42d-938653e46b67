#include <QDebug>
#include <algorithm>

#include "PrinterManager.h"

PrinterManager::PrinterManager(ConfigManager* configManager, QObject *parent)
    : QObject(parent)
    , m_functionModel(new ServiceFunctionModel(this))
    , m_statusModel(new ServiceStatusModel(this))
    , m_configManager(configManager)
{
    if (m_configManager) {
        m_selectedFileId = m_configManager->selectedFileId();
    }
}

PrinterManager::~PrinterManager()
{
    // Clean up functions and statuses
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        if (it.value() && it.value()->parent() == this) {
            delete it.value();
        }
    }

    for (auto it = m_statuses.begin(); it != m_statuses.end(); ++it) {
        if (it.value() && it.value()->parent() == this) {
            delete it.value();
        }
    }
}

QStringList PrinterManager::activeFunctions() const
{
    QStringList active;
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        if (it.value() && it.value()->isActive()) {
            active.append(it.value()->title());
        }
    }
    return active;
}

bool PrinterManager::hasActiveFunctions() const
{
    return std::any_of(m_functions.begin(), m_functions.end(),
                      [](ServiceFunction* func) { return func && func->isActive(); });
}

bool PrinterManager::startFunction(const QString &functionId)
{
    ServiceFunction *function = getFunction(functionId);
    if (!function) {
        qWarning() << "Function with ID" << functionId << "not found";
        return false;
    }

    if (!canStartFunction(function)) {
        qWarning() << "Cannot start function" << function->title() << "due to constraints";
        return false;
    }

    bool result = function->start();
    if (result) {
        updateFunctionConstraints();
    }
    return result;
}

bool PrinterManager::stopFunction(const QString &functionId)
{
    ServiceFunction *function = getFunction(functionId);
    if (!function) {
        qWarning() << "Function with ID" << functionId << "not found";
        return false;
    }

    bool result = function->stop();
    if (result) {
        updateFunctionConstraints();
    }
    return result;
}

void PrinterManager::stopAllFunctions()
{
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        if (it.value() && it.value()->isActive()) {
            it.value()->stop();
        }
    }
    updateFunctionConstraints();
}

ServiceFunction* PrinterManager::getFunction(const QString &functionId) const
{
    return m_functions.value(functionId, nullptr);
}

ServiceFunction* PrinterManager::getFunctionByTitle(const QString &title) const
{
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        if (it.value() && it.value()->title() == title) {
            return it.value();
        }
    }
    return nullptr;
}

ServiceStatus* PrinterManager::getStatus(const QString &name) const
{
    return m_statuses.value(name, nullptr);
}

void PrinterManager::updateStatus(const QString &name, const QVariant &value)
{
    ServiceStatus *status = getStatus(name);
    if (status) {
        status->setValue(value);
    } else {
        qWarning() << "Status" << name << "not found";
    }
}

void PrinterManager::registerFunction(ServiceFunction *function)
{
    if (!function) {
        return;
    }

    QString functionId = function->functionId();
    if (m_functions.contains(functionId)) {
        qWarning() << "Function with ID" << functionId << "already registered";
        return;
    }

    m_functions[functionId] = function;
    m_functionModel->addFunction(function);

    // Connect signals
    connect(function, &ServiceFunction::stateChanged, this, &PrinterManager::onFunctionStateChanged);
    connect(function, &ServiceFunction::functionStarted, this, &PrinterManager::onFunctionStarted);
    connect(function, &ServiceFunction::functionStopped, this, &PrinterManager::onFunctionStopped);
    connect(function, &ServiceFunction::functionCompleted, this, &PrinterManager::onFunctionCompleted);
    connect(function, &ServiceFunction::functionError, this, &PrinterManager::onFunctionError);

    updateFunctionConstraints();
}

void PrinterManager::registerStatus(ServiceStatus *status)
{
    if (!status) {
        return;
    }

    QString name = status->name();
    if (m_statuses.contains(name)) {
        qWarning() << "Status" << name << "already registered";
        return;
    }

    m_statuses[name] = status;
    m_statusModel->addStatus(status);

    // Connect signals
    connect(status, &ServiceStatus::thresholdExceeded, this, &PrinterManager::onStatusThresholdExceeded);
    connect(status, &ServiceStatus::thresholdRestored, this, &PrinterManager::onStatusThresholdExceeded);
}

void PrinterManager::unregisterFunction(ServiceFunction *function)
{
    if (!function) {
        return;
    }

    m_functions.remove(function->functionId());
    m_functionModel->removeFunction(function);
    disconnect(function, nullptr, this, nullptr);
    updateFunctionConstraints();
}

void PrinterManager::unregisterStatus(ServiceStatus *status)
{
    if (!status) {
        return;
    }

    m_statuses.remove(status->name());
    m_statusModel->removeStatus(status);
    disconnect(status, nullptr, this, nullptr);
}

void PrinterManager::onFunctionStateChanged()
{
    updateFunctionConstraints();
    emit activeFunctionsChanged();
}

void PrinterManager::onFunctionStarted()
{
    ServiceFunction *function = qobject_cast<ServiceFunction*>(sender());
    if (function) {
        emit functionStarted(function->functionId(), function->title());
    }
}

void PrinterManager::onFunctionStopped()
{
    ServiceFunction *function = qobject_cast<ServiceFunction*>(sender());
    if (function) {
        emit functionStopped(function->functionId(), function->title());
    }
}

void PrinterManager::onFunctionCompleted()
{
    ServiceFunction *function = qobject_cast<ServiceFunction*>(sender());
    if (function) {
        emit functionCompleted(function->functionId(), function->title());
    }
}

void PrinterManager::onFunctionError(const QString &errorMessage)
{
    ServiceFunction *function = qobject_cast<ServiceFunction*>(sender());
    if (function) {
        emit functionError(function->functionId(), function->title(), errorMessage);
    }
}

void PrinterManager::setStatusIndicators(bool system, bool warning, bool error)
{
    bool changed = false;
    if (m_statusIndicators[0] != system) { m_statusIndicators[0] = system; changed = true; }
    if (m_statusIndicators[1] != warning) { m_statusIndicators[1] = warning; changed = true; }
    if (m_statusIndicators[2] != error) { m_statusIndicators[2] = error; changed = true; }
    if (changed)
        emit statusIndicatorsChanged();
}

void PrinterManager::setStatusIndicator(int index, bool value)
{
    if (index < 0 || index > 2)
        return;
    if (m_statusIndicators[index] != value) {
        m_statusIndicators[index] = value;
        emit statusIndicatorsChanged();
    }
}

void PrinterManager::onStatusThresholdExceeded(ServiceStatus::StatusLevel /*level*/, const QVariant &value)
{
    ServiceStatus *status = qobject_cast<ServiceStatus*>(sender());
    if (status) {
        emit statusThresholdExceeded(status->name(), status->level(), value);

        // Check all statuses for their current levels
        bool anyCritical = false;
        bool anyWarning = false;
        for (auto it = m_statuses.begin(); it != m_statuses.end(); ++it) {
            ServiceStatus *s = it.value();
            if (!s) continue;
            if (s->level() == ServiceStatus::StatusLevel::Critical) {
                anyCritical = true;
                break;
            }
            if (s->level() == ServiceStatus::StatusLevel::Warning) {
                anyWarning = true;
            }
        }
        if (anyCritical) {
            setStatusIndicators(false, false, true);
        } else if (anyWarning) {
            setStatusIndicators(false, true, false);
        } else {
            setStatusIndicators(true, false, false);
        }
    }
}

void PrinterManager::updateFunctionConstraints()
{
    // Update canStart property for all functions
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        ServiceFunction *function = it.value();
        if (function) {
            function->setCanStart(canStartFunction(function));
        }
    }
}

bool PrinterManager::canStartFunction(ServiceFunction *function) const
{
    if (!function || function->isActive()) {
        return false;
    }

    // Check mutual exclusivity
    QStringList mutuallyExclusive = function->mutuallyExclusiveWith();
    for (auto it = m_functions.begin(); it != m_functions.end(); ++it) {
        ServiceFunction *otherFunction = it.value();
        if (otherFunction && otherFunction != function && otherFunction->isActive()) {
            if (mutuallyExclusive.contains(otherFunction->functionId())) {
                return false; // Cannot start due to mutual exclusivity
            }
        }
    }

    return true;
}

QString PrinterManager::selectedFileId() const
{
    return m_selectedFileId;
}

void PrinterManager::setSelectedFileId(const QString &fileId)
{
    if (m_selectedFileId != fileId) {
        m_selectedFileId = fileId;
        if (m_configManager) {
            m_configManager->setSelectedFileId(fileId);
        }
        emit selectedFileIdChanged(fileId);
    }
}

QString PrinterManager::currentPrintingFileId() const
{
    return m_currentPrintingFileId;
}

void PrinterManager::setCurrentPrintingFileId(const QString &fileId)
{
    if (m_currentPrintingFileId != fileId) {
        m_currentPrintingFileId = fileId;
        emit currentPrintingFileIdChanged(fileId);
    }
}
