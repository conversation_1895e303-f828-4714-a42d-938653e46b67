#ifndef SERVICEMANAGER_H
#define SERVICEMANAGER_H

#include <QObject>
#include <QMap>
#include <QList>
#include <QTimer>

#include "ConfigManager.h"
#include "models/ServiceFunctionModel.h"
#include "models/ServiceStatusModel.h"
#include "ServiceFunction.h"
#include "ServiceStatus.h"

class PrinterManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(ServiceFunctionModel* functionModel READ functionModel CONSTANT)
    Q_PROPERTY(ServiceStatusModel* statusModel READ statusModel CONSTANT)
    Q_PROPERTY(QStringList activeFunctions READ activeFunctions NOTIFY activeFunctionsChanged)
    Q_PROPERTY(bool hasActiveFunctions READ hasActiveFunctions NOTIFY activeFunctionsChanged)
    Q_PROPERTY(QVariantList statusIndicators READ statusIndicators NOTIFY statusIndicatorsChanged)
    Q_PROPERTY(QString selectedFileId READ selectedFileId WRITE setSelectedFileId NOTIFY selectedFileIdChanged)
    Q_PROPERTY(QString currentPrintingFileId READ currentPrintingFileId WRITE setCurrentPrintingFileId NOTIFY currentPrintingFileIdChanged)

public:
    explicit PrinterManager(ConfigManager* configManager = nullptr, QObject *parent = nullptr);
    virtual ~PrinterManager();

    ServiceFunctionModel* functionModel() const { return m_functionModel; }
    ServiceStatusModel* statusModel() const { return m_statusModel; }
    QStringList activeFunctions() const;
    bool hasActiveFunctions() const;

    QVariantList statusIndicators() const { return QVariantList() << m_statusIndicators[0] << m_statusIndicators[1] << m_statusIndicators[2]; }
    Q_INVOKABLE void setStatusIndicators(bool system, bool warning, bool error);
    Q_INVOKABLE void setStatusIndicator(int index, bool value);

public:
    // Function management
    Q_INVOKABLE bool startFunction(const QString &functionId);
    Q_INVOKABLE bool stopFunction(const QString &functionId);
    Q_INVOKABLE void stopAllFunctions();
    Q_INVOKABLE ServiceFunction* getFunction(const QString &functionId) const;
    Q_INVOKABLE ServiceFunction* getFunctionByTitle(const QString &title) const;

    // Registration methods
    void registerFunction(ServiceFunction *function);
    void unregisterFunction(ServiceFunction *function);
    void registerStatus(ServiceStatus *status);
    void unregisterStatus(ServiceStatus *status);

    // Status management
    Q_INVOKABLE ServiceStatus* getStatus(const QString &name) const;
    Q_INVOKABLE void updateStatus(const QString &name, const QVariant &value);

    QString selectedFileId() const;
    void setSelectedFileId(const QString &fileId);

    QString currentPrintingFileId() const;
    void setCurrentPrintingFileId(const QString &fileId);

signals:
    void activeFunctionsChanged();
    void functionStarted(const QString &functionId, const QString &title);
    void functionStopped(const QString &functionId, const QString &title);
    void functionCompleted(const QString &functionId, const QString &title);
    void functionError(const QString &functionId, const QString &title, const QString &errorMessage);
    void statusIndicatorsChanged();
    void selectedFileIdChanged(const QString &fileId);
    void currentPrintingFileIdChanged(const QString &fileId);
    void statusThresholdExceeded(const QString &statusName, ServiceStatus::StatusLevel level, const QVariant &value);

protected:
    QMap<QString, ServiceFunction*> m_functions;
    ServiceFunctionModel *m_functionModel;
    QMap<QString, ServiceStatus*> m_statuses;
    ServiceStatusModel *m_statusModel;
    bool m_statusIndicators[3] = {false, false, false};
    QString m_selectedFileId;
    QString m_currentPrintingFileId;
    ConfigManager* m_configManager = nullptr;


private slots:
    void onFunctionStateChanged();
    void onFunctionStarted();
    void onFunctionStopped();
    void onFunctionCompleted();
    void onFunctionError(const QString &errorMessage);
    void onStatusThresholdExceeded(ServiceStatus::StatusLevel level, const QVariant &value);

private:
    void updateFunctionConstraints();
    bool canStartFunction(ServiceFunction *function) const;

};

#endif // SERVICEMANAGER_H
