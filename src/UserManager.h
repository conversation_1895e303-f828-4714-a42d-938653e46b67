#ifndef USERMANAGER_H
#define USERMANAGER_H

#include <QObject>
#include <QMap>
#include <QString>

#include <Permission.h>
#include <UserInfo.h>

class UserInfo;

class UserManager : public QObject
{
    Q_OBJECT
    Q_PROPERTY(UserInfo currentUser READ currentUser NOTIFY currentUserChanged)
    Q_PROPERTY(QList<UserInfo> users READ users NOTIFY usersChanged)
    Q_PROPERTY(QStringList currentUserPermissions READ currentUserPermissions NOTIFY currentUserPermissionsChanged)

public:
    explicit UserManager(QObject *parent = nullptr);

    UserInfo currentUser() const;
    QStringList currentUserPermissions() const;
    QList<UserInfo> users() const;

    Q_INVOKABLE QList<Permission> getDefaultPermissions() const;
    Q_INVOKABLE QList<Permission> getAllowedPermissions(const QString& username) const;
    Q_INVOKABLE QList<Permission> getDisallowedPermissions(const QString& username) const;
    Q_INVOKABLE QStringList getAllPermissionGroups() const;

    Q_INVOKABLE void setCurrentUser(const QString &username);
    Q_INVOKABLE bool testPermission(const QString &permission) const;
    Q_INVOKABLE bool testPermission(const QString &permission, const QString &username) const;
    Q_INVOKABLE bool addPermission(const QString &permission);
    Q_INVOKABLE bool addPermission(const QString &permission, const QString &username);
    Q_INVOKABLE bool removePermission(const QString &permission);
    Q_INVOKABLE bool removePermission(const QString &permission, const QString &username);
    Q_INVOKABLE void reload();
    Q_INVOKABLE void loadUsers();
    Q_INVOKABLE bool saveUsers();

    Q_INVOKABLE UserInfo createUserInfo(const QString &username, const QString &password) const;
    Q_INVOKABLE bool addUser(const UserInfo &userInfo);
    Q_INVOKABLE bool userExists(const QString &username) const;
    Q_INVOKABLE bool deleteUser(const QString &username);

signals:
    void currentUserChanged();
    void currentUserPermissionsChanged();
    void usersChanged();

private:
    void initializePermissionsInfo();

private:
    QString m_username;
    QMap<QString, UserInfo> m_users;
    QMap<QString, Permission> m_permissionsInfo;
    QList<Permission> m_defaultPermissions;
};

#endif // USERMANAGER_H
