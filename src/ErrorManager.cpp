#include "Constants.h"
#include "ErrorManager.h"
#include <QVector>

ErrorManager* ErrorManager::s_instance = nullptr;

ErrorManager* ErrorManager::instance() {
    if (!s_instance)
        s_instance = new ErrorManager();
    return s_instance;
}

#include <QFile>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include <QCoreApplication>
#include <QDir>

ErrorManager::ErrorManager(QObject* parent)
    : QObject(parent) {
    loadErrorsFromFile();
}

const QVector<ErrorEntry>& ErrorManager::errors() const {
    return m_errors;
}

void ErrorManager::addError(const QString& error, const QString& code) {
    ErrorEntry entry{error, code, QDateTime::currentDateTime()};
    m_errors.append(entry);
    emit errorsChanged();
    saveErrorsToFile();
}

void ErrorManager::clearErrors() {
    m_errors.clear();
    emit errorsChanged();
    saveErrorsToFile();
}

void ErrorManager::resetErrors() {
    clearErrors();
    // Add any additional reset logic here
    saveErrorsToFile();
}

void ErrorManager::loadErrorsFromFile() {
    m_errors.clear();
    auto errorlogFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::ERRORLOG_FILE);
    QFile file(errorlogFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Could not open error log file:" << errorlogFilePath;
        return;
    }
    QByteArray data = file.readAll();
    file.close();
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(data, &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error in error log file:" << error.errorString();
        return;
    }
    if (!doc.isArray()) {
        qWarning() << "Error log file does not contain a JSON array";
        return;
    }
    QJsonArray arr = doc.array();
    for (const auto& val : arr) {
        QJsonObject obj = val.toObject();
        ErrorEntry entry;
        entry.error = obj["error"].toString();
        entry.code = obj["code"].toString();
        entry.timestamp = QDateTime::fromString(obj["timestamp"].toString(), Qt::ISODate);
        if (!entry.error.isEmpty() && !entry.code.isEmpty())
            m_errors.append(entry);
    }
    emit errorsChanged();
}

void ErrorManager::reloadErrorsFromFile() {
    loadErrorsFromFile();
}

void ErrorManager::saveErrorsToFile() {
    auto errorlogFilePath = QDir(QCoreApplication::applicationDirPath()).filePath(Constants::ERRORLOG_FILE);
    QFile file(errorlogFilePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Could not open error log file for writing:" << errorlogFilePath;
        return;
    }
    QJsonArray arr;
    for (const ErrorEntry& entry : m_errors) {
        QJsonObject obj;
        obj["error"] = entry.error;
        obj["code"] = entry.code;
        obj["timestamp"] = entry.timestamp.toString(Qt::ISODate);
        arr.append(obj);
    }
    QJsonDocument doc(arr);
    file.write(doc.toJson());
    file.close();
}
