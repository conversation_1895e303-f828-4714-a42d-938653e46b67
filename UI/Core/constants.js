// constants.js
.pragma library

const SCREEN = {
    // GENERAL
    HOME: "Home",
    LOG_IN: "LogIn",
    PRINT: "Print",
    PRINT_CREATE: "PrintCreate",
    PRINT_CREATE_USB: "PrintCreateUSB",
    PRINT_SETTINGS: "PrintSettings",
    PRINT_SELECT_SETTINGS: "printSelectSettings/PrintLayoutSelectSettings",

    // SPECIAL CONFIGURATIONS
    USERS: "Users",
    SAVE_CHANGES: "SaveChanges",
    JUST_GO_BACK: "JustGoBack",

    // INSERT DATA
    INSERT_DATA: "InsertData",
    INSERT_DATA_DATETIME: "insertDataDateTime/DateTimeLayout",
    INSERT_DATA_COUNTER: "insertDataCounter/CounterLayout",
    INSERT_DATA_TEXT: "insertData/TextInsert",
    INSERT_DATA_BARCODE: "insertData/BarcodeInsert",
    INSERT_DATA_SHIFT: "insertData/ShiftInsert",
    INSERT_DATA_METERING: "insertData/MeteringInsert",
    INSERT_DATA_BATCH_CODE: "insertData/BatchCodeInsert",
    
    
    // SERVICE
    SERVICE: "Service",
    SERVICE_FUNCTION: "service/Function",
    SERVICE_STATUS: "service/Status",
    SERVICE_RESET_MAINTENANCE: "service/ResetMaintenance",
    SERVICE_PHASE: "service/Phase",
    SERVICE_SERVICE: "service/Service",

    // SETTINGS
    SETTINGS: "Settings",
    SETTINGS_DATE_TIME: "settings/DateTime",
    SETTINGS_USER_PERMISSIONS: "settings/UserPermissions",
    SETTINGS_LANGUAGE: "settings/Language",
    SETTINGS_USERS: "settings/Users",
    SETTINGS_ADD_USER: "settings/AddUser",
    SETTINGS_SYSTEM_INFO: "settings/SystemInfo",
    SETTINGS_QR_DATA: "settings/QRData",
    SETTINGS_OTHER: "settings/Other"
};

// You can add more groups, e.g. for asset paths, colors, etc.
var ASSET_PATH = {
    // ICONS (SVG)
    ICONS_BASE: "icons/",
    ICON_TRASH: "icons/Trash.svg",
    ICON_ADD_USER: "icons/add-user.svg",
    ICON_BACK: "icons/back.svg",
    ICON_BRIGHTNESS: "icons/brightness.svg",
    ICON_CARD_STATUS_CIRCLES: "icons/card-status-circles.svg",
    ICON_CHECKMARK_LANGUAGE: "icons/checkmark-language.svg",
    ICON_CHECKMARK: "icons/checkmark.svg",
    ICON_CHEVRON_DOWN: "icons/chevron-down.svg",
    ICON_CREATE: "icons/create.svg",
    ICON_EDIT: "icons/edit.svg",
    ICON_FORBIDDEN: "icons/forbidden.svg",
    ICON_HOME_SMALL: "icons/home-small.svg",
    ICON_LEFT_ARROW_BLACK: "icons/left-arrow-black.svg",
    ICON_LEFT_ARROW: "icons/left-arrow.svg",
    ICON_LOGOUT: "icons/logout.svg",
    ICON_MINUS: "icons/minus.svg",
    ICON_NIGHT_MODE: "icons/night-mode.svg",
    ICON_PLUS: "icons/plus.svg",
    ICON_POWER_BUTTON_BACKGROUND: "icons/power-button-background.svg",
    ICON_POWER_BUTTON_HUE: "icons/power-button-hue.svg",
    ICON_PRINT: "icons/print.svg",
    ICON_RELOAD: "icons/reload.svg",
    ICON_RIGHT_ARROW_WHITE: "icons/right-arrow-white.svg",
    ICON_SAVE_CHANGES: "icons/save-changes.svg",
    ICON_SELECT: "icons/select.svg",
    ICON_SERVICE: "icons/service.svg",
    ICON_STOP: "icons/stop.svg",
    ICON_SYSTEM: "icons/system.svg",
    ICON_USER_CIRCLE: "icons/user-circle.svg",
    ICON_USER: "icons/user.svg",
    ICON_HOME: "icons/home.svg",
    ICON_WARNING_YELLOW: "icons/warning-yellow.svg",
    ICON_WARNING_GREY: "icons/warning-grey.svg",
    ICON_POWER_BUTTON: "icons/power-button.svg",
    ICON_ERROR_REFRESH: "icons/error_refresh.svg",
    ICON_ERROR_CLEAR: "icons/error_clear.svg",
    ICON_ERROR_RESET: "icons/error_rest.svg",

    // IMAGES
    IMAGE_ADD_CIRCLE: "Images/add-circle.png",
    IMAGE_EDIT_RECTANGLE: "Images/edit-rectangle.png",
    IMAGE_TRASH: "Images/trash.png",
    IMAGE_NUMBER_CIRCLE_ONE: "Images/NumberCircleOne.png",
    IMAGE_EXCHANGE: "Images/exchange.png",
    IMAGE_RELOAD: "Images/reload.png",
    IMAGE_HALF: "Images/half.png",
    IMAGE_BOLD: "Images/bold.png",
    IMAGE_INSERT: "Images/insert.png",
    IMAGE_USB: "Images/usb.png",
    IMAGE_ARROW: "Images/arrow.png",

    // BACKGROUNDS
    BG_LOGIN: "backgrounds/login-background.png",
    BG_HOME: "backgrounds/home-background.png",

    // LOGOS
    LOGO_PROSPR: "logos/prospr-logo.svg"
};
