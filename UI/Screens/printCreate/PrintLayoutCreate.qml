import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Theme" as Theme

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    property Item selectedItem: null

    signal navigateToRequested(string to, string from)
    signal deletePopupRequested()
    signal backPopupRequested()
    signal testButtonCreateClicked()

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        anchors.margins: 20
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent

            // Top Panel - Print Preview
            TopPanelCreate {
                id: topPanel

                height: parent.height * 0.3
                onItemSelected: printLayout.selectedItem = item

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }

            }

            // Left Panel - File Selection
            LeftPanelCreate {
                id: leftPanel

                width: parent.width * 0.6 - 10
                onSelectButtonClicked: {
                    console.log("CLICKED!!!!!!!!!!!!!!!!!!!!!!!");
                    navigateToRequested("printSelectSettings/PrintLayoutSelectSettings", "PrintLayoutCreate");
                }

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }

                Connections {
                    // Emit the new signal from PrintLayoutCreate

                    // This function name matches the signal from LeftPanelCreate
                    function onDeleteButtonClicked() {
                        printLayout.deletePopupRequested();
                    }

                    target: leftPanel // Connect to the LeftPanelCreate instance
                }

            }

            // Right Panel - Print Controls and Status
            RightPanelCreate {
                id: rightPanel

                onTestButtonClicked: testButtonCreateClicked()
                onLeftButtonClicked: {
                    if (topPanel.selectedItem !== null)
                        topPanel.selectedItem.xOffset -= 5;

                }
                onRightButtonClicked: {
                    if (topPanel.selectedItem !== null)
                        topPanel.selectedItem.xOffset += 5;

                }
                onUpButtonClicked: {
                    if (topPanel.selectedItem !== null)
                        topPanel.selectedItem.yOffset -= 5;

                }
                onDownButtonClicked: {
                    if (topPanel.selectedItem !== null)
                        topPanel.selectedItem.yOffset += 5;

                }

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

            }

            // Forward navigateToRequested signal from LeftPanel
            Connections {
                function onNavigateToRequested(to, from) {
                    printScreen.navigateToRequested(to, from);
                }

                target: leftPanel
            }

            // Connections to handle data passing between panels
            Connections {
                function onFileSelected(fileName, lotNum, bestByDate) {
                    topPanel.updatePreview(fileName, lotNum, bestByDate);
                }

                target: leftPanel
            }

        }

    }

}
