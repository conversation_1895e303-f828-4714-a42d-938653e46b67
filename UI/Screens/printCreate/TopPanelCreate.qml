import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printPreviewSection

    // Property to track the currently selected item
    property Item selectedItem: null

    signal itemSelected(Item item)

    color: Theme.Colors.white
    radius: Theme.Radius.large

    Item {
        anchors.fill: parent
        anchors.margins: 30
        clip: true

        Rectangle {
            id: lotNumberRect

            property int xOffset: 20
            property int yOffset: parent.height - (height * 2) - 20

            color: "transparent"
            border.color: printPreviewSection.selectedItem === lotNumberRect ? Theme.Colors.primary : Theme.Colors.transparent
            border.width: printPreviewSection.selectedItem === lotNumberRect ? 1 : 0
            radius: 6
            width: lotNumberText.implicitWidth + 20
            height: lotNumberText.implicitHeight + 10
            x: xOffset
            y: yOffset

            Text {
                id: lotNumberText

                anchors.centerIn: parent
                text: "LOT #12345"
                font.pixelSize: 30
                font.weight: printPreviewSection.selectedItem === lotNumberRect ? Font.Bold : Font.Normal
                color: Theme.Colors.black
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    printPreviewSection.selectedItem = lotNumberRect;
                    printPreviewSection.itemSelected(lotNumberRect);
                }
            }

        }

        Rectangle {
            id: bestByDateRect

            property int xOffset: 20
            property int yOffset: parent.height - height - 20

            color: "transparent"
            border.color: printPreviewSection.selectedItem === bestByDateRect ? Theme.Colors.primary : Theme.Colors.transparent
            border.width: printPreviewSection.selectedItem === bestByDateRect ? 1 : 0
            radius: 6
            width: bestByDateText.implicitWidth + 20
            height: bestByDateText.implicitHeight + 10
            x: xOffset
            y: yOffset

            Text {
                id: bestByDateText

                anchors.centerIn: parent
                text: "BEST BUY 02/17/26"
                font.pixelSize: 30
                font.weight: printPreviewSection.selectedItem === bestByDateRect ? Font.Bold : Font.Normal
                color: Theme.Colors.black
            }

            MouseArea {
                anchors.fill: parent
                onClicked: {
                    printPreviewSection.selectedItem = bestByDateRect;
                    printPreviewSection.itemSelected(bestByDateRect);
                }
            }

        }

    }

}
