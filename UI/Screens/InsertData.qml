import QtQuick 2.14
import QtQuick.Controls 2.14
import QtQuick.Layouts 1.14
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Item {
    id: insertDataScreen

    ColumnLayout {
        anchors.fill: parent
        anchors.margins: Theme.Spacing.screenMargin
        spacing: 48

        RowLayout {
            Layout.fillWidth: true
            spacing: 32

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/text.png")
                text: "Text"
                onClicked: NavigationManager.go("insertData/TextInsert", "InsertData")
            }

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/calculator.png")
                text: "Counter"
                onClicked: NavigationManager.go("insertDataCounter/CounterLayout", "InsertData")
            }

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/calendar.png")
                text: "Date & Time"
                onClicked: NavigationManager.go("insertDataDateTime/DateTimeLayout", "InsertData")
            }

        }

        RowLayout {
            Layout.fillWidth: true
            spacing: 32

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/batch-code.png")
                text: "Batch Code"
                onClicked: NavigationManager.go("insertData/BatchCodeInsert", "InsertData")
            }

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/metering.png")
                text: "Metering"
                onClicked: NavigationManager.go("insertData/MeteringInsert", "InsertData")
            }

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/barcode.png")
                text: "Barcode"
                onClicked: NavigationManager.go("insertData/BarcodeInsert", "InsertData")
            }

            CardButton {
                Layout.fillWidth: true
                iconSource: PathResolver.resolveAsset("Images/shift.png")
                text: "Shift"
                onClicked: NavigationManager.go("insertData/ShiftInsert", "InsertData")
            }

        }

    }

}
