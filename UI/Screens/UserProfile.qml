import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme

Rectangle {
    // Bottom bar is now managed by MainLayout.qml

    id: userProfileScreen

    color: "transparent"

    Image {
        source: PathResolver.resolveAsset(CONST.ASSET_PATH.BG_LOGIN)
        anchors.fill: parent
        fillMode: Image.PreserveAspectCrop
        z: -1
    }

    // Prospr logo on the left
    Item {
        id: leftSection

        width: parent.width * 0.3
        height: parent.height
        anchors.left: parent.left

        Column {
            anchors.centerIn: parent
            spacing: 10

            // Prospr text logo
            Text {
                text: "Prospr"
                font.pixelSize: 60
                font.italic: true
                font.family: "cursive"
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // Tagline
            Text {
                text: "CALI · USA · EST. 1997"
                font.pixelSize: 12
                font.letterSpacing: 2
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // Separator line
            Rectangle {
                width: 200
                height: 1
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

            // Slogan
            Text {
                text: "MARK AMBITIOUSLY."
                font.pixelSize: 12
                font.letterSpacing: 2
                color: Theme.Colors.white
                anchors.horizontalCenter: parent.horizontalCenter
            }

        }

    }

    // User profile card
    Rectangle {
        id: profileCard

        width: parent.width * 0.45
        height: parent.height * 0.5
        radius: 10
        color: Theme.Colors.white
        anchors.centerIn: parent
        // Shadow effect
        layer.enabled: true

        // Content
        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 15

            // Profile section
            RowLayout {
                Layout.fillWidth: true

                // User avatar
                Rectangle {
                    width: 120
                    height: 120
                    color: Theme.Colors.secondary
                    radius: 5

                    // User icon
                    Text {
                        anchors.centerIn: parent
                        text: "👤"
                        font.pixelSize: 60
                        color: Theme.Colors.white
                    }

                }

                // User info
                ColumnLayout {
                    Layout.fillWidth: true
                    Layout.leftMargin: 20
                    spacing: 5

                    // User name
                    Text {
                        text: "Alexander Richardson"
                        font.pixelSize: 26
                        font.weight: Font.Medium
                        color: Theme.Colors.white
                    }

                    // Permission link
                    Text {
                        text: "View Permission"
                        font.pixelSize: 18
                        color: Theme.Colors.red
                        font.underline: true

                        MouseArea {
                            // View permissions action

                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                            }
                        }

                    }

                }

            }

            Item {
                Layout.fillHeight: true
            }

            // Button row
            RowLayout {
                Layout.fillWidth: true
                spacing: 15

                // Switch User button
                Button {
                    // Switch user action

                    id: switchUserButton

                    Layout.fillWidth: true
                    height: 40
                    onClicked: {
                    }

                    contentItem: RowLayout {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "⭐"
                            font.pixelSize: 16
                            color: Theme.Colors.white
                        }

                        Text {
                            text: "SWITCH USER"
                            font.pixelSize: 14
                            color: Theme.Colors.white
                        }

                    }

                    background: Rectangle {
                        color: Theme.Colors.secondary
                        radius: 5
                    }

                }

                // Log Out button
                Button {
                    id: logoutButton

                    Layout.fillWidth: true
                    height: 40
                    onClicked: {
                        NavigationManager.go("LogIn");
                    }

                    contentItem: RowLayout {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "🔒"
                            font.pixelSize: 16
                            color: Theme.Colors.white
                        }

                        Text {
                            text: "LOG OUT"
                            font.pixelSize: 14
                            color: Theme.Colors.white
                        }

                    }

                    background: Rectangle {
                        color: Theme.Colors.red
                        radius: 5
                    }

                }

                // Go to Home button
                Button {
                    id: homeButton

                    Layout.fillWidth: true
                    height: 40
                    onClicked: {
                        NavigationManager.go("Home");
                    }

                    contentItem: RowLayout {
                        anchors.centerIn: parent
                        spacing: 10

                        Text {
                            text: "🏠"
                            font.pixelSize: 16
                            color: Theme.Colors.white
                        }

                        Text {
                            text: "GO TO HOME"
                            font.pixelSize: 14
                            color: Theme.Colors.white
                        }

                    }

                    background: Rectangle {
                        color: Theme.Colors.red
                        radius: 5
                    }

                }

            }

        }

        layer.effect: DropShadow {
            horizontalOffset: 0
            verticalOffset: 3
            radius: 8
            samples: 17
            color: Theme.Colors.black
        }

    }

}
