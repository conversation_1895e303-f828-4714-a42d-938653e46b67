/*
  ErrorLog Screen Component
  Displays the error log interface with a panel for viewing logs
  and controls for navigation.
*/

import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Components/errorlog"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
import "qrc:/UI/Theme" as Theme
import Backend.Error 1.0
import Backend.PrinterManager 1.0

Rectangle {
    anchors.fill: parent
    color: 'darkgrey'

    function refreshData() {
        if (typeof ErrorManager !== 'undefined' && ErrorManager.reloadErrorsFromFile)
            ErrorManager.reloadErrorsFromFile();
        console.log("Refreshing error log data...");
    }

    // TODO: implement pop-up confirmation
    function clearLog() {
        if (typeof ErrorManager !== 'undefined' && ErrorManager.clearErrors)
            ErrorManager.clearErrors();
    }

    // TODO: implement pop-up confirmation
    function resetLog() {
        if (typeof PrinterManager !== 'undefined' && PrinterManager.setStatusIndicators)
            PrinterManager.setStatusIndicators(true, false, false); // green, grey, grey
    }

    TitledFrame {
        id: errorLogScreen

        anchors.fill: parent
        anchors.margins: Theme.Spacing.screenMargin
        title: "Error Log"
        titleHorizontalAlignment: Text.AlignLeft

        // Main error log panel
        ErrorLogPanel {
            id: errorLogPanel

            anchors.fill: parent
            anchors.leftMargin: 30
            anchors.rightMargin: 30
            anchors.bottomMargin: 1
        }

    }

    // --- Top-right action buttons: REFRESH, CLEAR, RESET ---
    Row {
        spacing: 12
        anchors {
            right: errorLogScreen.right
            top: errorLogScreen.top
            rightMargin: 30
            topMargin: 10
        }
        // REFRESH button
        SquareButton {
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_RELOAD)
            onClicked: refreshData()
            Accessible.name: "Refresh Error Log"
        }
        // CLEAR button
        SquareButton {
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_ERROR_CLEAR)
            onClicked: clearLog()
            Accessible.name: "Clear Error Log"
        }
        // RESET button
        SquareButton {
            image.source: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_ERROR_RESET)
            onClicked: resetLog()
            Accessible.name: "Reset Error Log"
        }
    }

}
