import QtQuick 2.15
import QtQuick.Controls 2.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Core/constants.js" as CONST
// Import our modular components
import "qrc:/UI/Screens/printCreate"
import "qrc:/UI/Theme" as Theme

Rectangle {
    id: printScreen

    property bool testPopupVisible: false

    signal navigateToRequested(string to, string from)

    gradient: Theme.Colors.backgroundGradient

    // Main content layout using the modular PrintLayout component
    PrintLayoutCreate {
        id: printLayout

        onTestButtonCreateClicked: {
            testPopupVisible = true;
            mainLayout.popupVisible = true;
        }
        anchors.fill: parent
        z: 0 // Ensure content is below the blur overlay

        Connections {
            // This function name matches the signal from PrintLayoutCreate
            function onDeletePopupRequested() {
                deletePopup.open();
                mainLayout.popupVisible = true;
            }

            target: printLayout // Connect to the PrintLayoutCreate instance
        }

    }

    Connections {
        function onBackPopupRequested() {
            backPopup.open();
            mainLayout.popupVisible = true;
        }

        target: bottomBar
    }

    // Use the new BlurOverlay component
    BlurOverlay {
        anchors.fill: parent
        source: printLayout // Set the source to the PrintLayoutCreate
        visible: mainLayout.popupVisible
    }

    QPopup {
        // Add your delete logic here

        id: deletePopup

        confirmationText: "Are you sure you want to delete (message name)?"
        button1Text: "DELETE"
        button2Text: "CANCEL"
        button2Color: Theme.Colors.black
        numberOfButtons: 2
        button1ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_TRASH)
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        onButton1Pressed: {
            console.log("Delete confirmed");
            deletePopup.close(); //Close the popup
            mainLayout.popupVisible = false;
            navigateToRequested("Print", "PrintCreate");
        }
        onButton2Pressed: {
            console.log("Delete canceled");
            deletePopup.close(); // Close the popup
            mainLayout.popupVisible = false;
        }
    }

    QPopup {
        id: backPopup

        confirmationText: "Are you sure you want to back from edit?"
        button1Text: "SAVE CHANGES"
        button2Text: "DON’T SAVE CHANGES"
        button1Color: Theme.Colors.black
        button2Color: Theme.Colors.red
        numberOfButtons: 2
        button1ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_SAVE_CHANGES)
        button2ImageSource: PathResolver.resolveAsset(CONST.ASSET_PATH.ICON_FORBIDDEN)
        onButton1Pressed: {
            console.log("Save changes confirmed");
            backPopup.close(); //Close the popup
            mainLayout.popupVisible = false;
            navigateToRequested("Print", "PrintCreate");
        }
        onButton2Pressed: {
            console.log("Changes canceled");
            backPopup.close(); // Close the popup
            mainLayout.popupVisible = false;
            navigateToRequested("Print", "PrintCreate");
        }
    }

    TestPrintMode {
        id: testPrintPopup

        visible: testPopupVisible
        onClosePopup: {
            mainLayout.popupVisible = false;
            testPopupVisible = false;
        }
    }

}
