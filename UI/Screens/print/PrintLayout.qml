import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme
import "qrc:/UI/Core/constants.js" as CONST
import Backend.UserManager 1.0
import Backend.PrinterManager 1.0

// Main print layout component that organizes the three panels
Item {
    id: printLayout

    // Main content area with shadow and rounded corners
    Rectangle {
        id: mainContent

        anchors.fill: parent
        color: "transparent" // Use transparent since parent already has gradient
        radius: Theme.Radius.large

        // Main layout with three panels
        Item {
            anchors.fill: parent
            anchors.margins: 20

            Component.onCompleted: {
                if (fileListModel.count > 0) {
                    if (PrinterManager.selectedFileId !== "") {
                        leftPanel.selectFileId(PrinterManager.selectedFileId);
                    }
                    if (PrinterManager.currentPrintingFileId !== "") {
                        leftPanel.setFilePrintingState(PrinterManager.currentPrintingFileId, true);
                    }
                }
            }

            // Shared file list model for all panels
            ListModel {
                id: fileListModel

                ListElement {
                    fileId: "file01"
                    name: "Test File 01"
                    status: ""
                    lotNum: "LOT #12345"
                    bestByDate: "BEST BY 02/17/26"
                }

                ListElement {
                    fileId: "file02"
                    name: "Test File 02"
                    status: ""
                    lotNum: "LOT #54321"
                    bestByDate: "BEST BY 03/01/27"
                }

                ListElement {
                    fileId: "file03"
                    name: "Test File 03"
                    status: ""
                    lotNum: "LOT #67890"
                    bestByDate: "BEST BY 04/15/28"
                }

                ListElement {
                    fileId: "file04"
                    name: "Test File 04"
                    status: ""
                    lotNum: "LOT #11111"
                    bestByDate: "BEST BY 05/10/29"
                }

                ListElement {
                    fileId: "file05"
                    name: "Test File 05"
                    status: ""
                    lotNum: "LOT #22222"
                    bestByDate: "BEST BY 06/20/30"
                }

                ListElement {
                    fileId: "file06"
                    name: "Test File 06"
                    status: ""
                    lotNum: "LOT #33333"
                    bestByDate: "BEST BY 07/30/31"
                }

            }

            // Print Preview Panel
            Rectangle {
                id: topPanel

                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                }
                height: parent.height * 0.3

                // Defined property for the preview content
                property string currentPreviewContent: "Select a file to view print preview"
                property bool showGridLines: gridLinesSwitch.checked
                property int gridLinesSize: 36 // size in pixels
                property color gridLinesColor: Theme.Colors.secondary

                // Signal when preview is updated
                signal previewUpdated()

                // Function to update preview when a file is selected
                function updatePreview(fileId) {
                    // Lookup file by ID in fileListModel
                    var file = null;
                    for (var i = 0; i < fileListModel.count; ++i) {
                        var f = fileListModel.get(i);
                        if (f.fileId === fileId) {
                            file = f;
                            break;
                        }
                    }
                    if (file) {
                        previewContent.text = "Previewing file: " + file.name;
                        lotNumber.text = file.lotNum;
                        bestByDate.text = file.bestByDate;
                        lotNumber.visible = true;
                        bestByDate.visible = true;
                    } else {
                        previewContent.text = "File not found";
                        lotNumber.visible = false;
                        bestByDate.visible = false;
                    }
                    previewUpdated();
                }

                color: Theme.Colors.white
                radius: Theme.Radius.large
                clip: true

                // Grid lines
                Item {
                    anchors.fill: parent
                    visible: topPanel.showGridLines

                    // Vertical grid lines
                    Repeater {
                        model: parent.width / topPanel.gridLinesSize + 1

                        Rectangle {
                            x: index * topPanel.gridLinesSize
                            width: 1
                            height: topPanel.height
                            color: topPanel.gridLinesColor
                            opacity: 0.3
                        }
                    }

                    // Horizontal grid lines
                    Repeater {
                        model: parent.height / topPanel.gridLinesSize + 1

                        Rectangle {
                            y: index * topPanel.gridLinesSize
                            width: topPanel.width
                            height: 1
                            color: topPanel.gridLinesColor
                            opacity: 0.3
                        }
                    }
                }

                // Mark at bottom left corner using two rectangles
                Item {
                    anchors.left: parent.left
                    anchors.bottom: parent.bottom
                    width: 20
                    height: 20

                    // Horizontal line
                    Rectangle {
                        anchors.left: parent.left
                        anchors.bottom: parent.bottom
                        width: parent.width
                        height: 3
                        color: Theme.Colors.red
                    }

                    // Vertical line
                    Rectangle {
                        anchors.left: parent.left
                        anchors.bottom: parent.bottom
                        width: 3
                        height: parent.height
                        color: Theme.Colors.red
                    }
                }

                ColumnLayout {
                    // Placeholder for the actual preview content
                    anchors.left: parent.left
                    anchors.bottom: parent.bottom
                    anchors.margins: 30
                    Layout.alignment: Qt.AlignLeft | Qt.AlignBottom

                    Text {
                        id: previewContent

                        text: "Select a file to view print preview"
                        font.pixelSize: 24
                        color: Theme.Colors.secondary
                        Layout.fillWidth: true
                    }

                    Text {
                        id: lotNumber

                        text: "LOT #12345"
                        font.pixelSize: 40
                        font.weight: Font.Bold
                        color: Theme.Colors.black
                        visible: false // Will show when a file is selected
                    }

                    Text {
                        id: bestByDate

                        text: "BEST BUY 02/17/26"
                        font.pixelSize: 40
                        font.weight: Font.Bold
                        color: Theme.Colors.black
                        visible: false // Will show when a file is selected
                    }
                }

            }

            // Left Panel - File Selection
            Rectangle {
                id: leftPanel

                anchors {
                    top: topPanel.bottom
                    left: parent.left
                    bottom: parent.bottom
                    topMargin: 20
                }
                width: parent.width * 0.6 - 10

                // --- API: Signals for navigation/actions ---
                signal navigateToRequested(string to, string from)
                // Signal to notify when a file is selected (by ID)
                signal fileSelected(string fileId)
                signal deleteButtonClicked(string fileId, string currentFileId)

                // Helper: find file object by ID
                function getFileById(fileId) {
                    for (var i = 0; i < fileListModel.count; ++i) {
                        var file = fileListModel.get(i);
                        if (file.fileId === fileId)
                            return file;

                    }
                    return null;
                }

                // Helper to remove file and update selection by ID
                function deleteCurrentFile() {
                    if (fileListModel.count === 0 || fileListView.currentIndex < 0) {
                        console.log("[LeftPanel] No file selected to delete");
                        return ;
                    }
                    if (fileListModel.get(fileListView.currentIndex).status === "Printing") {
                        console.log("[LeftPanel] Can't delete file that is currently printing");

                        deletePopup.open()

                        return ;
                    }
                    var idx = fileListView.currentIndex;
                    var fileId = fileListModel.get(idx).fileId;
                    fileListModel.remove(idx);
                    // Determine new selection
                    var newIndex = Math.min(idx, fileListModel.count - 1);
                    var newCurrentFileId = "";
                    if (fileListModel.count > 0 && newIndex >= 0) {
                        var newFile = fileListModel.get(newIndex);
                        fileListView.currentIndex = newIndex;
                        fileSelected(newFile.fileId);
                        newCurrentFileId = newFile.fileId;
                    } else {
                        fileListView.currentIndex = -1;
                    }
                    // Emit delete signal to layout
                    deleteButtonClicked(fileId, newCurrentFileId);
                    console.log('[Toast] File with ID "' + fileId + '" deleted. Please select undo if mistake!');
                }

                function setFilePrintingState(fileId, isPrinting) {
                    for (var i = 0; i < fileListModel.count; ++i) {
                        var file = fileListModel.get(i);
                        if (file && file.fileId === fileId) {
                            fileListModel.setProperty(i, "status", isPrinting ? "Printing" : "");

                            break;
                        }
                    }
                }

                function selectFileId(fileId) {
                    for (var i = 0; i < fileListModel.count; ++i) {
                        var file = fileListModel.get(i)
                        if (file && file.fileId === fileId) {
                            fileListView.currentIndex = i;
                            fileSelected(fileId);

                            break;
                        }
                    }
                }

                radius: Theme.Radius.xxxlarge
                color: Theme.Colors.white

                Text {
                    id: fileHeaderText

                    text: "File"
                    font.family: Theme.Fonts.youngSerif
                    font.pixelSize: 46
                    font.weight: Font.Bold
                    color: Theme.Colors.black

                    anchors {
                        top: parent.top
                        left: parent.left
                        topMargin: 30
                        leftMargin: 30
                        rightMargin: 30
                    }

                }

                // File List View
                ListView {
                    id: fileListView

                    clip: true
                    spacing: Theme.Spacing.small
                    model: fileListModel

                    anchors {
                        top: fileHeaderText.bottom
                        left: parent.left
                        right: actionsColumn.left
                        bottom: parent.bottom
                        topMargin: 10
                        leftMargin: 30
                        rightMargin: 30
                        bottomMargin: 10
                    }

                    delegate: Rectangle {
                        id: fileDelegate

                        // Access the file object for this index directly from fileListModel
                        property var fileObj: fileListModel.get(index)
                        // Cache these conditions to avoid recalculating multiple times
                        readonly property bool isSelected: fileObj ? fileObj.fileId === PrinterManager.selectedFileId : false
                        readonly property bool isPrinting: fileObj ? fileObj.status === "Printing" : false

                        width: ListView.view.width - 15
                        height: 74
                        // Keep status and selection separate
                        // If it has a printing status, it's orange regardless of selection
                        // If it's selected but not printing, use blue highlight
                        color: isPrinting ? Theme.Colors.orange : (isSelected ? Qt.darker(Theme.Colors.selected, 1.1) : Theme.Colors.noSelected)
                        // Add a border for selected items, even if they have a status
                        radius: Theme.Radius.xmlarge

                        Text {
                            id: fileTextFromListView

                            text: fileObj ? fileObj.name : ""
                            color: fileObj && fileObj.status === "Printing" ? Theme.Colors.white : Theme.Colors.black
                            font.weight: Font.Medium
                            font.pixelSize: 28

                            anchors {
                                left: parent.left
                                verticalCenter: parent.verticalCenter
                                margins: 10
                            }

                        }

                        // Dot mark
                        Rectangle {
                            visible: isSelected
                            width: 16
                            height: 16
                            radius: 8
                            color: Theme.Colors.primary
                            anchors {
                                left: fileTextFromListView.right
                                verticalCenter: parent.verticalCenter
                                leftMargin: 10
                            }
                        }

                        // Status badge
                        Rectangle {
                            visible: fileObj.status !== ""
                            width: statusText.width + 16
                            height: 34
                            radius: Theme.Radius.xxmlarge
                            color: Theme.Colors.white
                            border.width: 1
                            border.color: Theme.Colors.borderCol

                            anchors {
                                left: fileTextFromListView.right
                                verticalCenter: parent.verticalCenter
                                margins: 20
                            }

                            Text {
                                id: statusText

                                anchors.centerIn: parent
                                text: fileObj.status
                                color: Theme.Colors.orange
                                font.pixelSize: 20
                                font.weight: Font.Medium
                            }

                        }

                        MouseArea {
                            anchors.fill: parent
                            onClicked: {
                                fileListView.currentIndex = index;
                                leftPanel.fileSelected(fileObj.fileId);
                            }
                        }

                    }

                    ScrollBar.vertical: ScrollBar {
                        policy: ScrollBar.AsNeeded // Shows only when needed
                        width: 10
                        anchors.right: parent.right

                        background: Rectangle {
                            color: "transparent"
                        }

                        contentItem: Rectangle {
                            color: Theme.Colors.statusLightGrey // Light gray for the scrollbar handle
                            radius: Theme.Radius.large
                        }

                    }

                }

                // Action buttons column
                ColumnLayout {
                    id: actionsColumn

                    width: 330
                    spacing: Theme.Spacing.large

                    anchors {
                        top: fileHeaderText.bottom
                        right: parent.right
                        bottom: parent.bottom
                        rightMargin: 30
                        bottomMargin: 30
                    }

                    // CREATE button
                    LeftPanelButton {
                        text: "CREATE"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_ADD_CIRCLE)
                        onClicked: {
                            console.log("Create button clicked");
                            NavigationManager.go(CONST.SCREEN.PRINT_CREATE, CONST.SCREEN.PRINT);
                        }
                    }

                    // EDIT button
                    LeftPanelButton {
                        text: "EDIT"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_EDIT_RECTANGLE)
                        onClicked: {
                            console.log("Edit button clicked");
                            // TODO but pass the current file (name, state, metadata, etc..) to edit page
                            NavigationManager.go(CONST.SCREEN.PRINT_CREATE, CONST.SCREEN.PRINT);
                        }
                    }

                    // DELETE button
                    LeftPanelButton {
                        text: "DELETE"
                        source: PathResolver.resolveAsset(CONST.ASSET_PATH.IMAGE_TRASH)
                        onClicked: {
                            leftPanel.deleteCurrentFile();
                        }
                    }

                    // PRINTER SETTING button
                    LeftPanelButton {
                        text: "PRINTER SETTING"
                        onClicked: {
                            console.log("Printer Setting button clicked");
                            NavigationManager.go(CONST.SCREEN.PRINT_SETTINGS, CONST.SCREEN.PRINT);
                        }
                    }

                    SwitchControl {
                        id: gridLinesSwitch

                        Layout.preferredHeight: 40
                        Layout.fillWidth: true

                        text: "GRID LINES"
                        font.weight: Font.Bold
                        textColor: Theme.Colors.white

                        textAlignment: Text.AlignHCenter
                        backgroundRectangle.radius: 12
                        backgroundRectangle.color: Theme.Colors.darkBlue
                        backgroundRectangle.border.width: 0
                        font.pixelSize: 24
                        padding: 10

                        indicator: SwitchIndicator {
                            anchors.right: parent.right
                            anchors.verticalCenter: parent.verticalCenter
                            anchors.rightMargin: 10

                            width: height * 2
                            height: gridLinesSwitch.height / 2

                            checked: parent.checked
                        }
                    }


                }

            }

            // Print Controls and Status Panel
            Item {
                id: rightPanel

                anchors {
                    top: topPanel.bottom
                    left: leftPanel.right
                    right: parent.right
                    bottom: parent.bottom
                    topMargin: 20
                    leftMargin: 20
                }

                // Properties to expose print settings to other components
                property int copies: 1
                property int startAt: 1
                property int endAt: 100

                // Signals
                signal printRequested()
                signal settingsChanged()
                signal printButtonPressed()
                signal stopButtonPressed()

                // Print Controls Section (upper part)
                Rectangle {
                    id: printControlsSection

                    height: parent.height * 0.6
                    radius: Theme.Radius.large
                    color: "transparent"

                    anchors {
                        top: parent.top
                        left: parent.left
                        right: parent.right
                    }

                    // Controls layout
                    Column {
                        spacing: Theme.Spacing.small

                        anchors {
                            fill: parent
                            margins: 15
                        }

                        // Print Range inputs
                        Row {
                            width: parent.width
                            height: 50
                            spacing: Theme.Spacing.small

                            // Start At
                            Column {
                                visible: UserManager.currentUser.role !== "Guest"
                                width: (parent.width - parent.spacing) / 2
                                spacing: Theme.Spacing.xxsmall

                                Text {
                                    text: "Start At"
                                    font.pixelSize: 28
                                    color: Theme.Colors.secondary
                                }

                                Rectangle {
                                    width: parent.width
                                    height: 74
                                    radius: Theme.Radius.xmlarge
                                    color: Theme.Colors.white

                                    TextInput {
                                        id: startAtInput

                                        text: "500"
                                        color: Theme.Colors.black
                                        font.pixelSize: 28
                                        verticalAlignment: TextInput.AlignVCenter
                                        selectByMouse: true

                                        anchors {
                                            fill: parent
                                            margins: 8
                                        }

                                        validator: IntValidator {
                                            bottom: 1
                                            top: 1e+09
                                        }

                                    }

                                }

                            }

                            // End At
                            Column {
                                visible: UserManager.currentUser.role !== "Guest"
                                width: (parent.width - parent.spacing) / 2
                                spacing: Theme.Spacing.xxsmall

                                Text {
                                    text: "End At"
                                    font.pixelSize: 28
                                    color: Theme.Colors.secondary
                                }

                                Rectangle {
                                    width: parent.width
                                    height: 74
                                    radius: Theme.Radius.xmlarge
                                    color: Theme.Colors.white

                                    TextInput {
                                        id: endAtInput

                                        text: "1200"
                                        color: Theme.Colors.black
                                        font.pixelSize: 28
                                        verticalAlignment: TextInput.AlignVCenter
                                        selectByMouse: true

                                        anchors {
                                            fill: parent
                                            margins: 8
                                        }

                                        validator: IntValidator {
                                            bottom: 1
                                            top: 1e+09
                                        }

                                    }

                                }

                            }

                        }

                        // Print and Stop buttons
                        Item {
                            width: parent.width
                            height: 200

                            Row {
                                id: buttonRow

                                anchors.horizontalCenter: parent.horizontalCenter // Center the row
                                spacing: Theme.Spacing.small
                                topPadding: 60
                                height: parent.height

                                // PRINT button
                                Rectangle {
                                    width: buttonRow.height
                                    height: buttonRow.height
                                    radius: height / 2
                                    color: Qt.lighter(Theme.Colors.statusGreen, 1.2)

                                    Rectangle {
                                        anchors.centerIn: parent
                                        width: parent.height - 20
                                        height: parent.height - 20
                                        radius: height / 2
                                        color: Theme.Colors.statusGreen

                                        Text {
                                            anchors.centerIn: parent
                                            text: "PRINT"
                                            font.family: Theme.Fonts.youngSerif
                                            font.pixelSize: 48
                                            font.weight: Font.Bold
                                            color: Theme.Colors.white
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            enabled: !PrinterManager.statusIndicators[2] // disable when error state
                                            onClicked: {
                                                console.log("Print button clicked");
                                                printStatusValue.text = "Printing";
                                                printStatusValue.color = Theme.Colors.orange; // Orange
                                                rightPanel.printButtonPressed();
                                            }
                                        }

                                    }

                                }

                                // STOP button
                                Rectangle {
                                    width: buttonRow.height
                                    height: buttonRow.height
                                    radius: height / 2
                                    color: Qt.lighter(Theme.Colors.statusRed, 1.2)

                                    Rectangle {
                                        anchors.centerIn: parent
                                        width: parent.height - 20
                                        height: parent.height - 20
                                        radius: height / 2
                                        color: Theme.Colors.statusRed

                                        Text {
                                            anchors.centerIn: parent
                                            text: "STOP"
                                            font.family: Theme.Fonts.youngSerif
                                            font.pixelSize: 48
                                            font.weight: Font.Bold
                                            color: Theme.Colors.white
                                        }

                                        MouseArea {
                                            anchors.fill: parent
                                            onClicked: {
                                                console.log("Stop button clicked");
                                                printStatusValue.text = "Stopped";
                                                printStatusValue.color = Theme.Colors.darkRed; // Red
                                                rightPanel.stopButtonPressed();
                                            }
                                        }

                                    }

                                }

                            }

                        }

                    }

                }

                // Print Status Section (lower part)
                Rectangle {
                    id: statusSection

                    radius: Theme.Radius.large
                    border.color: Theme.Colors.orange
                    border.width: 1
                    color: Theme.Colors.borderCol
                    height: 116 // Fixed height set to 116

                    anchors {
                        top: printControlsSection.bottom
                        left: parent.left
                        right: parent.right
                        topMargin: 15
                    }

                    Column {
                        spacing: Theme.Spacing.xsmall
                        anchors.fill: parent
                        anchors.margins: 10

                        // Header row: Labels
                        Row {
                            width: parent.width
                            height: 40

                            Repeater {
                                model: ["Status", "Printed", "History"]

                                delegate: Rectangle {
                                    width: parent.width / 3
                                    height: parent.height
                                    color: "transparent"

                                    Text {
                                        anchors.centerIn: parent
                                        text: modelData
                                        font.pixelSize: 28
                                        font.weight: Font.DemiBold
                                        color: Theme.Colors.black
                                    }

                                    Rectangle {
                                        anchors.top: parent.top
                                        anchors.topMargin: -10
                                        anchors.right: parent.right
                                        width: index < 2 ? 1 : 0
                                        height: 116
                                        color: Theme.Colors.orange
                                    }

                                }

                            }

                        }

                        // Value row
                        Row {
                            width: parent.width
                            height: 40

                            // Status value with pill
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Rectangle {
                                    width: 111
                                    height: 34
                                    radius: Theme.Radius.large
                                    color: Theme.Colors.white
                                    anchors.centerIn: parent
                                    border.color: "transparent"

                                    Text {
                                        id: printStatusValue

                                        anchors.centerIn: parent
                                        text: "Stopped"
                                        font.pixelSize: 20
                                        font.weight: Font.DemiBold
                                        color: Theme.Colors.darkRed
                                    }

                                }

                            }

                            // Printed count
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Text {
                                    id: printedCountValue

                                    anchors.centerIn: parent
                                    text: "520000"
                                    font.pixelSize: 28
                                    font.weight: Font.Bold
                                    color: Theme.Colors.orange
                                }

                            }

                            // History count
                            Rectangle {
                                width: parent.width / 3
                                height: parent.height
                                color: "transparent"

                                Text {
                                    id: historyCountValue

                                    anchors.centerIn: parent
                                    text: "410000"
                                    font.pixelSize: 28
                                    font.weight: Font.Bold
                                    color: Theme.Colors.orange
                                }

                            }

                        }

                    }

                }

                // RESET COUNTER button
                Rectangle {
                    id: resetCounterButton

                    width: parent.width
                    height: 78
                    radius: Theme.Radius.xmlarge
                    color: Theme.Colors.secondary

                    anchors {
                        top: statusSection.bottom
                        left: parent.left
                        right: parent.right
                        topMargin: 15
                    }

                    Text {
                        anchors.centerIn: parent
                        text: "RESET COUNTER"
                        font.pixelSize: 30
                        font.weight: Font.Bold
                        color: Theme.Colors.white
                    }

                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            // Reset counter
                            console.log("Reset counter clicked");
                            printedCountValue.text = "0";
                        }
                    }

                }

            }

            Connections {
                function onFileSelected(fileId) {
                    PrinterManager.selectedFileId = fileId;
                    topPanel.updatePreview(fileId);
                }

                function onDeleteButtonClicked(fileId, currentFileId) {
                    PrinterManager.selectedFileId = currentFileId;
                    topPanel.updatePreview(currentFileId);
                }

                target: leftPanel
            }

            Connections {
                function onPrintButtonPressed() {
                    if (PrinterManager.currentPrintingFileId === "" && PrinterManager.selectedFileId !== "") {
                        leftPanel.setFilePrintingState(PrinterManager.selectedFileId, true);
                        PrinterManager.currentPrintingFileId = PrinterManager.selectedFileId;
                    }
                }

                function onStopButtonPressed() {
                    if (PrinterManager.currentPrintingFileId !== "") {
                        leftPanel.setFilePrintingState(PrinterManager.currentPrintingFileId, false);
                        leftPanel.selectFileId(PrinterManager.selectedFileId)
                        PrinterManager.currentPrintingFileId = "";
                    }
                }

                target: rightPanel
            }

        }

    }

    BlurOverlay {
        anchors.fill: mainContent
        source: mainContent
        visible: deletePopup.visible
    }

    QPopup {
        id: deletePopup

        confirmationText: "Cannot delete file currently printing"
        button1Text: "OK"
        button1Color: Theme.Colors.black
        numberOfButtons: 1
        onButton1Pressed: {
            deletePopup.close(); //Close the popup
        }
        onOpened: {
            mainLayout.popupVisible = true;
        }
        onClosed: {
            mainLayout.popupVisible = false;
        }
    }
}
