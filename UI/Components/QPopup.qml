import QtGraphicalEffects 1.15
import QtQuick 2.15
import QtQuick.Controls 2.0
import "qrc:/UI/Theme" as Theme

Popup {
    id: popup

    // Properties for customizable text
    property string confirmationText: ""
    property string button1Text: ""
    property string button2Text: ""
    property string button3Text: ""
    property alias button1ImageSource: button1.source
    property alias button2ImageSource: button2.source
    property alias button3ImageSource: button3.source
    // Properties for customizable colors
    property color popupColor: Theme.Colors.white
    property color button1Color: Theme.Colors.red
    property color button2Color: Theme.Colors.black
    property color button3Color: Theme.Colors.lightGrey
    property color button1TextColor: Theme.Colors.white
    property color button2TextColor: Theme.Colors.white
    property color button3TextColor: Theme.Colors.black
    property int numberOfButtons: 2

    // Signals for button presses
    signal button1Pressed()
    signal button2Pressed()
    signal button3Pressed()

    x: parent.width / 2 - width / 2
    y: parent.height / 2 - height / 2
    width: 620
    height: 300 + (100 * numberOfButtons)
    focus: true
    closePolicy: Popup.CloseOnEscape

    background: Rectangle {
        anchors.fill: parent
        color: "transparent"
    }

    contentItem: Rectangle {
        width: 600
        height: 488
        radius: Theme.Radius.xxxlarge
        color: popupColor
        anchors.centerIn: parent
        anchors.verticalCenterOffset: 55

        Text {
            text: confirmationText
            font.pixelSize: 40
            font.family: Theme.Fonts.youngSerif
            width: parent.width - 100
            wrapMode: Text.WordWrap

            anchors {
                horizontalCenter: parent.horizontalCenter
                top: parent.top
                topMargin: 50
                leftMargin: 50
                rightMargin: 50
            }

        }

        LeftPanelButton {
            id: button1

            width: 500
            color: button1Color
            text: button1Text
            textColor: button1TextColor
            source: button2ImageSource
            onClicked: {
                console.log("Button 1 clicked");
                button1Pressed();
            }

            anchors {
                horizontalCenter: parent.horizontalCenter
                top: parent.top
                topMargin: 212
                leftMargin: 50
                rightMargin: 50
            }

        }

        LeftPanelButton {
            id: button2

            width: 500
            color: button2Color
            text: button2Text
            textColor: button2TextColor
            source: button2ImageSource
            visible: numberOfButtons > 1
            onClicked: {
                console.log("Button 2 clicked");
                button2Pressed();
            }

            anchors {
                horizontalCenter: parent.horizontalCenter
                top: button1.bottom
                topMargin: 25
                leftMargin: 50
                rightMargin: 50
            }

        }

        LeftPanelButton {
            id: button3

            width: 500
            color: button3Color
            text: button3Text
            textColor: button3TextColor
            source: button3ImageSource
            visible: numberOfButtons > 2
            onClicked: {
                console.log("Button 3 clicked");
                button3Pressed();
            }

            anchors {
                horizontalCenter: parent.horizontalCenter
                top: button2.bottom
                topMargin: 25
                leftMargin: 50
                rightMargin: 50
            }

        }

    }

}
