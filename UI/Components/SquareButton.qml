import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/UI/Components"
import "qrc:/UI/Core"
import "qrc:/UI/Theme" as Theme

Button {
    id: root

    property alias image: img
    property alias backgroundRectangle: bck

    implicitHeight: 100
    implicitWidth: 100
    opacity: enabled ? 1 : 0.3

    contentItem: Item {
        Image {
            id: img

            anchors.centerIn: parent
        }

    }

    background: Rectangle {
        id: bck

        color: Theme.Colors.darkBlue
        radius: 20
    }

}
