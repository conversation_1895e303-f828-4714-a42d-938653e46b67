.import "qrc:/UI/Core/constants.js" as CONST

.pragma library

const SERVICE_BTN = { label: "SERVICE", icon: "service.svg", screen: "Service" }
const PRINT_BTN = { label: "PRINT", icon: "print.svg", screen: "Print" }
const SYSTEM_BTN = { label: "SYSTEM", icon: "system.svg", screen: "Settings" }
const BACK_BTN = function(screen) { return { label: "BACK", icon: "back.svg", screen: screen } }

const S = CONST.SCREEN

// Nav button arrays for each screen
var navButtonConfigs = {
    // GENERAL
    [S.HOME]: [ SERVICE_BTN, PRINT_BTN, SYSTEM_BTN ],
    [S.LOG_IN]: [],
    [S.PRINT]: [ SERVICE_BTN, PRINT_BTN, SYSTEM_BTN ],
    [S.PRINT_CREATE]: [ BACK_BTN(S.PRINT), { label: "SAVE", icon: "save-changes.svg", screen: "" } ],
    [S.PRINT_CREATE_USB]: [ BACK_BTN(S.PRINT_CREATE), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.PRINT_SELECT_SETTINGS]: [ BACK_BTN(S.PRINT_CREATE), { label: "SELECT", icon: "edit.svg", action: "select" }, { label: "CREATE", icon: "save-changes.svg", screen: "PrintSettings" }, { label: "EDIT", icon: "edit.svg", screen: "PrintSettings" }, { label: "DELETE", icon: "Trash.svg", action: "delete", screen: "" } ],
    [S.PRINT_SETTINGS]: [ BACK_BTN(S.PRINT), { label: "SAVE", icon: "save-changes.svg", screen: "" } ],

    // SPECIAL CONFIGURATIONS
    [S.USERS]: [ BACK_BTN(S.SETTINGS), { label: "ADD USER", icon: "add-user.svg", screen: "settings/AddUser" }, { label: "EDIT USER", icon: "save-changes.svg", screen: "settings/UserPermissions" }, { label: "DELETE USER", icon: "Trash.svg", action: "delete", screen: "" } ],
    [S.SAVE_CHANGES]: [ BACK_BTN(S.SETTINGS), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.JUST_GO_BACK]: [ BACK_BTN(S.INSERT_DATA) ],
    
    // INSERT DATA
    [S.INSERT_DATA]: [ BACK_BTN(S.PRINT_CREATE) ],
    [S.INSERT_DATA_DATETIME]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_DATETIME } ],
    [S.INSERT_DATA_COUNTER]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_COUNTER } ],
    [S.INSERT_DATA_TEXT]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_TEXT } ],
    [S.INSERT_DATA_BARCODE]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_BARCODE } ],
    [S.INSERT_DATA_SHIFT]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_SHIFT } ],
    [S.INSERT_DATA_METERING]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_METERING } ],
    [S.INSERT_DATA_BATCH_CODE]: [ BACK_BTN(S.INSERT_DATA), { label: "SAVE", icon: "save-changes.svg", action: "save", screen: S.INSERT_DATA_BATCH_CODE } ],
    
    // SERVICE
    [S.SERVICE]: [ SERVICE_BTN, PRINT_BTN, SYSTEM_BTN ],
    [S.SERVICE_FUNCTION]: [ BACK_BTN(S.SERVICE) ],
    [S.SERVICE_STATUS]: [ BACK_BTN(S.SERVICE) ],
    [S.SERVICE_RESET_MAINTENANCE]: [ BACK_BTN(S.SERVICE) ],
    [S.SERVICE_PHASE]: [ BACK_BTN(S.SERVICE), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.SERVICE_SERVICE]: [ BACK_BTN(S.SERVICE), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    
    // SETTINGS
    [S.SETTINGS]: [ SERVICE_BTN, PRINT_BTN, SYSTEM_BTN ],
    [S.SETTINGS_DATE_TIME]: [ BACK_BTN(S.SETTINGS), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.SETTINGS_USER_PERMISSIONS]: [ BACK_BTN(S.SETTINGS), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.SETTINGS_LANGUAGE]: [ BACK_BTN(S.SETTINGS), { label: "SAVE CHANGES", icon: "save-changes.svg", action: "save", screen: "" } ],
    [S.SETTINGS_USERS]: [ BACK_BTN(S.SETTINGS), { label: "ADD USER", icon: "add-user.svg", screen: "settings/AddUser" }, { label: "EDIT USER", icon: "save-changes.svg", action: "edit", screen: "" }, { label: "DELETE USER", icon: "Trash.svg", action: "delete", screen: "" } ],
    [S.SETTINGS_ADD_USER]: [ BACK_BTN(S.SETTINGS) ],
    [S.SETTINGS_SYSTEM_INFO]: [ BACK_BTN(S.SETTINGS)],
    [S.SETTINGS_QR_DATA]: [ BACK_BTN(S.SETTINGS)],
    [S.SETTINGS_OTHER]: [ BACK_BTN(S.SETTINGS)],
};

function getNavButtonsForScreen(screenName) {
    return navButtonConfigs[screenName] || navButtonConfigs[S.HOME] || [];
}
