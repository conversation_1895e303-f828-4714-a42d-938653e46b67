# Prospr Light — Architecture Overview

This document provides a high-level overview of the Prospr Light application's architecture, including its main components, data flow, and integration points. A Mermaid diagram is included for quick visual reference.

---

## System Overview

Prospr Light is a Qt5/QML-based industrial printer management application. It is designed for both desktop development (Ubuntu 22.04) and embedded deployment (Allwinner T507). The system architecture emphasizes modularity, separation of UI and business logic, and easy adaptation to different environments.

---

## Architectural Layers

- **C++ Backend**
  - Entry point (`main.cpp`): Initializes the application, loads fonts, registers C++ types for QML, and launches the QML engine.
  - Custom C++ types (e.g., `TimeHandler`) registered for use in QML.

- **QML Frontend**
  - Root QML file (`main.qml`): Loads the main UI and controllers.
  - Core logic handled by singletons in `UI/Core` (e.g., `AppController`, `ContentLoader`, `PathResolver`).
  - Theming and design tokens provided by `UI/Theme` singletons (e.g., `Colors`, `Typography`).
  - UI screens and components are modular and dynamically loaded.

- **Scripts & Deployment**
  - Shell scripts in `scripts/` for setup, build, run, and deployment.
  - Environment-specific logic for embedded targets (resource paths, environment variables).

---

## Component Diagram

```mermaid
flowchart TD
    subgraph "C++ Backend"
        A1[main.cpp] --> A2[QFontDatabase / Font Loading]
        A1 --> A3[Register C++ Types]
        A1 --> A4[Launch QQmlApplicationEngine]
        A3 -->|e.g. TimeHandler| B2
    end
    
    subgraph "QML Frontend"
        B1[main.qml] --> B2[AppController UI/Core]
        B2 --> B3[ContentLoader UI/Core]
        B2 --> B4[DisplayConfig, PlatformHelpers]
        B3 --> B5[Dynamic Screens / UI_new/Screens]
        B3 --> B6[Reusable Components / UI_new/Components]
        B1 --> B7[Theme Singletons UI/Theme]
        B2 --> B7
        B3 --> B7
    end
    
    A4 --> B1
```

---

## Key Data Flow
- **Startup:** `main.cpp` → loads fonts, registers types → starts `main.qml`.
- **UI Initialization:** `main.qml` → instantiates `AppController` → loads screens via `ContentLoader`.
- **Theming:** All QML files import `UI/Theme` for consistent design.
- **Resource Paths:** `PathResolver` ensures correct asset loading for desktop/embedded.
- **C++/QML Integration:** Custom types (like `TimeHandler`) are available in QML for backend logic.

---

## Environment-Specific Logic
- **Desktop:** Uses `qrc:/` resource paths, desktop-specific scaling.
- **Embedded (T507):** Uses `file:///` paths, sets framebuffer and font env vars, runs full-screen.

---

## References
- See `DEVELOPER_GUIDE.md` for onboarding and workflow.
- See `README.md` for build and run instructions.
- See `scripts/` for automation and deployment logic.
