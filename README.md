# Light - CIJ Printer Management Application

Light is a Qt5/QML-based application for printer management and label printing. It's designed to run on Linux (Ubuntu 22.04) and embedded targets (Allwinner T507 aarch64).

## Features

- Modern, streamlined UI with consistent navigation
- Multiple view screens including Print Preview, Print Settings, and Printer Settings
- Configuration interface for printer settings
- Label creation and management
- File management capabilities
- User management system
- Error information and handling

## Project Structure

```
light/
├── main.qml                  # Main application QML file
├── main.cpp                  # C++ entry point
├── src/                      # C++ components
│   └── timehandler.cpp       # Time handling component
├── UI_new/                   # New QML UI components
│   ├── Components/           # Reusable UI components
│   ├── Screens/              # Application screens
│   └── Theme/                # Theming systems
├── tests/                    # Test suite for the application
│   ├── qmltests/            # Pure QML/JavaScript tests
│   └── uitests/             # UI component tests with rendering
├── scripts/                  # Build and setup scripts
├── 3rdparty/                 # External dependencies and submodules
│   └── cutekeyboard/         # On-screen keyboard component
└── build/                    # Build output directory
```

## Submodule Setup

This project uses git submodules. Before building for the first time, make sure to initialize and update all submodules:

```bash
git submodule update --init --recursive
```

If you pull new changes that update submodules, run the above command again to ensure all submodules are up to date.

## Building and Running

```bash
# (Optional) Set up the development environment and install dependencies
./scripts/setup_dev_env.sh

# Build the application (debug build for local development)
./scripts/build.sh -t x86_64 -b debug -c

# Run the application
./scripts/run.sh
```

## Requirements

See [scripts/setup_dev_env.sh](scripts/setup_dev_env.sh) for package requirements.

#### Imports used (Some of them)

- Qt 5.15.4 (target) or Qt 5.15.x (development)
- QtQuick 2.15
- QtQuick.Controls 2.15
- QtTest module for running tests


The build script will check for these dependencies and prompt you to install any missing packages.

## Testing

TODO


## 3rdparty

#### cutekeyboard: On-screen keyboard component

This is a modified version of the cutekeyboard component from the Qt Examples. It is used to provide an on-screen keyboard for the application.
```
cd 3rdparty/cutekeyboard/src
qmake OR /usr/local/opt/qt@5/bin/qmake
make -j$(sysctl -n hw.logicalcpu)
make install
```