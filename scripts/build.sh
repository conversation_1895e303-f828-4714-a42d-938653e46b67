#!/bin/bash

# Exit on error
set -e

# Colors for output
GREEN="\033[0;32m"
RED="\033[0;31m"
YELLOW="\033[0;33m"
BLUE="\033[0;34m"
NC="\033[0m" # No Color

# Get script directory and project root
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
APP_NAME="light_qt5"  # Name of the binary

# Default build configuration
TARGET="x86_64"      # Default to x86_64 target (local)
BUILD_TYPE="debug"    # Default to debug build
CLEAN=true           # Default to clean before building
SKIP_CUTEKBD=false

print_usage() {
    echo -e "${BLUE}Usage: $0 [OPTIONS]${NC}"
    echo -e "Options:"
    echo -e "  -t, --target TARGET   Build target: 'aarch64' (cross-compile) or 'x86_64' (default, local)"
    echo -e "  -b, --build TYPE      Build type: 'release' or 'debug' (default)"
    echo -e "  -c, --clean           Clean before building (default)"
    echo -e "  --no-clean            Skip cleaning before building"
    echo -e "  --skip-qrc            Skip auto-generation of qml.qrc (default: always rebuild)"
    echo -e "  --skip-cutekeyboard   Skip building cutekeyboard submodule"
    echo -e "  -h, --help            Display this help message"
}

# Parse command line arguments
SKIP_QRC=false
while [[ $# -gt 0 ]]; do
    case $1 in
        -t|--target)
            TARGET="$2"
            shift 2
            ;;
        -b|--build)
            BUILD_TYPE="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        --no-clean)
            CLEAN=false
            shift
            ;;
        --skip-qrc)
            SKIP_QRC=true
            shift
            ;;
        --skip-cutekeyboard)
            SKIP_CUTEKBD=true
            shift
            ;;
        -h|--help)
            print_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            print_usage
            exit 1
            ;;
    esac
done

# --- Pre-build: Auto-generate qml.qrc unless skipped ---
generate_qrc() {
    # --- Configuration ---
    local SUBFOLDERS_TO_INCLUDE=("Core" "Theme" "Assets" "Screens" "Components")
    local FILE_EXTENSIONS_TO_INCLUDE=("qml" "js" "png" "svg" "jpg" "jpeg")

    # --- Build find condition from extensions ---
    local find_conditions=""
    for ext in "${FILE_EXTENSIONS_TO_INCLUDE[@]}"; do
        if [ -n "$find_conditions" ]; then
            find_conditions+=" -o "
        fi
        find_conditions+="-name '*.${ext}'"
    done

    # --- Path setup ---
    local QRC_PATH="$PROJECT_DIR/qml.qrc"
    local UI_DIR="$PROJECT_DIR/UI"
    if [[ ! -d "$UI_DIR" ]]; then
        UI_DIR="$PROJECT_DIR/light/UI"
        if [[ ! -d "$UI_DIR" ]]; then
            echo -e "${RED}❌ Error: UI directory not found at $UI_DIR or $PROJECT_DIR/light/UI${NC}"
            exit 1
        fi
    fi

    # --- QRC generation ---
    echo -e "${BLUE}🔄 Regenerating qml.qrc from UI directories...${NC}"
    local QRC_TMP_PATH="$QRC_PATH.new"
    echo "<RCC>" > "$QRC_TMP_PATH"
    echo "    <qresource prefix=\"/\">" >> "$QRC_TMP_PATH"
    echo "        <file>main.qml</file>" >> "$QRC_TMP_PATH"
    # Dynamically include all qmldir files under UI (recursively)
    find "$UI_DIR" -name 'qmldir' | while read -r qmldirfile; do
        relf="${qmldirfile#$PROJECT_DIR/}"
        echo "        <file>$relf</file>" >> "$QRC_TMP_PATH"
    done

    # Top-level UI files
    eval "find \"$UI_DIR\" -maxdepth 1 -type f \( $find_conditions \)" | while read -r f; do
        relf="${f#$PROJECT_DIR/}"
        echo "        <file>$relf</file>" >> "$QRC_TMP_PATH"
    done

    # Sort <file> entries (excluding main.qml) and reconstruct qml.qrc
    { 
        grep -v '<file>' "$QRC_TMP_PATH"; 
        echo "        <file>main.qml</file>";
        grep '<file>' "$QRC_TMP_PATH" | grep -v 'main.qml' | sort;
    } > "$QRC_TMP_PATH.sorted"

    # If qml.qrc exists, create a sorted version for comparison
    if [[ -f "$QRC_PATH" ]]; then
        { 
            grep -v '<file>' "$QRC_PATH"; 
            echo "        <file>main.qml</file>";
            grep '<file>' "$QRC_PATH" | grep -v 'main.qml' | sort;
        } > "$QRC_PATH.sorted"
        if ! diff -q "$QRC_PATH.sorted" "$QRC_TMP_PATH.sorted" > /dev/null; then
            mv "$QRC_TMP_PATH.sorted" "$QRC_PATH"
            echo -e "${GREEN}✅ qml.qrc updated (sorted and changed)${NC}"
        else
            echo -e "${YELLOW}⚠️  No changes to qml.qrc (after sorting)${NC}"
        fi
        rm -f "$QRC_PATH.sorted" "$QRC_TMP_PATH" "$QRC_TMP_PATH.sorted"
    else
        mv "$QRC_TMP_PATH.sorted" "$QRC_PATH"
        rm -f "$QRC_TMP_PATH"
        echo -e "${GREEN}✅ qml.qrc created (sorted)${NC}"
    fi

    # Subfolders to include
    for sub in "${SUBFOLDERS_TO_INCLUDE[@]}"; do
        if [ -d "$UI_DIR/$sub" ]; then
            eval "find \"$UI_DIR/$sub\" -type f \( $find_conditions \)" | sort | while read -r f; do
                relf="${f#$PROJECT_DIR/}"
                echo "        <file>$relf</file>" >> "$QRC_PATH"
            done
        fi
    done

    echo "    </qresource>" >> "$QRC_PATH"
    echo "</RCC>" >> "$QRC_PATH"
    echo -e "${GREEN}✅ qml.qrc regenerated with all UI resources.${NC}"
}

if [[ "$SKIP_QRC" != true ]]; then
    generate_qrc
else
    echo -e "${YELLOW}⚠️ Skipping qml.qrc auto-generation as requested.${NC}"
fi

# Validate arguments
if [[ "$TARGET" != "x86_64" && "$TARGET" != "aarch64" ]]; then
    echo -e "${RED}Invalid target: $TARGET${NC}"
    echo -e "${YELLOW}Supported targets are: 'aarch64' or 'x86_64'${NC}"
    exit 1
fi

if [[ "$BUILD_TYPE" != "debug" && "$BUILD_TYPE" != "release" ]]; then
    echo -e "${RED}Invalid build type: $BUILD_TYPE${NC}"
    echo -e "${YELLOW}Supported build types are: 'debug' or 'release'${NC}"
    exit 1
fi

# Set platform-specific build directory
BUILD_DIR="$SCRIPT_DIR/../build/$TARGET-$BUILD_TYPE"
mkdir -p "$BUILD_DIR"

# Locate the .pro file, checking both possible paths
echo -e "${BLUE}🔎 Locating project file...${NC}"
PRO_FILE1="$PROJECT_DIR/light_qt5.pro"
PRO_FILE2="$PROJECT_DIR/light/light_qt5.pro"

if [[ -f "$PRO_FILE1" ]]; then
    PRO_FILE="$PRO_FILE1"
elif [[ -f "$PRO_FILE2" ]]; then
    PRO_FILE="$PRO_FILE2"
else
    echo -e "${RED}❌ Error: Cannot find .pro file. Tried:${NC}"
    echo -e "${RED}    - $PRO_FILE1${NC}"
    echo -e "${RED}    - $PRO_FILE2${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Project file found: $PRO_FILE${NC}"

# Clean if requested
if [[ "$CLEAN" == true ]]; then
    echo -e "${YELLOW}🧹 Cleaning previous build for $TARGET-$BUILD_TYPE...${NC}"
    rm -rf "$BUILD_DIR"/*
fi

BINARY="$BUILD_DIR/$APP_NAME"
MAKE_JOBS=$(nproc 2>/dev/null || echo 4)

build_cutekeyboard() {
    # Use local variables to avoid polluting global scope
    local QMAKE="$1"
    local MAKE_JOBS="$2"
    local CUTEKBD_DIR CUTEKBD_BUILD_DIR MASTER_PLATFORMINPUTCONTEXTS_DIR MASTER_QML_DIR
    if [[ "$SKIP_CUTEKBD" != true ]]; then
        echo -e "${BLUE}🔧 Building cutekeyboard submodule...${NC}"
        CUTEKBD_DIR="$PROJECT_DIR/3rdparty/cutekeyboard"
        CUTEKBD_BUILD_DIR="$CUTEKBD_DIR/build"
        mkdir -p "$CUTEKBD_BUILD_DIR"
        cd "$CUTEKBD_BUILD_DIR"
        "$QMAKE" "$CUTEKBD_DIR/cutekeyboard.pro"
        make -j"$MAKE_JOBS"
        echo -e "${GREEN}✅ cutekeyboard submodule built successfully.${NC}"

        # Copy libcutekeyboardplugin.so to master build directory
        MASTER_PLATFORMINPUTCONTEXTS_DIR="$BUILD_DIR/platforminputcontexts"
        mkdir -p "$MASTER_PLATFORMINPUTCONTEXTS_DIR"
        if [[ -f "$CUTEKBD_BUILD_DIR/src/libcutekeyboardplugin.so" ]]; then
            cp "$CUTEKBD_BUILD_DIR/src/libcutekeyboardplugin.so" "$MASTER_PLATFORMINPUTCONTEXTS_DIR/"
            echo -e "${GREEN}✅ Copied libcutekeyboardplugin.so to $MASTER_PLATFORMINPUTCONTEXTS_DIR${NC}"
        else
            echo -e "${YELLOW}⚠️ libcutekeyboardplugin.so not found in $CUTEKBD_BUILD_DIR/src${NC}"
        fi

        # Copy all QML files from cutekeyboard to master build directory
        MASTER_QML_DIR="$BUILD_DIR/QtQuick/CuteKeyboard"
        mkdir -p "$MASTER_QML_DIR"
        if compgen -G "$CUTEKBD_DIR/src/qml/*" > /dev/null; then
            cp -r "$CUTEKBD_DIR/src/qml/"* "$MASTER_QML_DIR/"
            echo -e "${GREEN}✅ Copied CuteKeyboard QML files to $MASTER_QML_DIR${NC}"
        else
            echo -e "${YELLOW}⚠️ No QML files found in $CUTEKBD_DIR/src/qml${NC}"
        fi
        echo -e "${BLUE}🔙 Returning to project root: $SCRIPT_DIR${NC}"
        cd "$BUILD_DIR"
    else
        echo -e "${YELLOW}⚠️ Skipping cutekeyboard submodule build as requested.${NC}"
    fi
}

if [[ "$TARGET" == "aarch64" ]]; then
    echo -e "${YELLOW}🏗️ Building for aarch64 target with ${BUILD_TYPE} configuration...${NC}"

    # Source target environment if exists
    ENV_DIR="$PROJECT_DIR/scripts/env"
    if [[ -f "$ENV_DIR/target.sh" ]]; then
        echo -e "${BLUE}📋 Sourcing target environment...${NC}"
        source "$ENV_DIR/target.sh"
    fi

    # Use Docker for cross-compilation via qt_aarch64_builder
    DOCKER_DIR="$PROJECT_DIR/qt_aarch64_builder"
    if [[ -f "$DOCKER_DIR/Makefile" ]]; then
        echo -e "${BLUE}🐳 Using Docker-based build environment for cross-compilation...${NC}"

        # Create output directory for build artifacts
        OUTPUT_DIR="$BUILD_DIR"
        mkdir -p "$OUTPUT_DIR"
        echo -e "${BLUE}💿 Build output will be in: $OUTPUT_DIR${NC}"

        # Copy the .pro file to the docker workspace if it's not already there
        if [[ ! -f "$DOCKER_DIR/light_qt5.pro" ]]; then
            echo -e "${BLUE}📋 Copying project files to Docker workspace...${NC}"
            cp "$PROJECT_DIR/light_qt5.pro" "$DOCKER_DIR/"
        fi

        # Run qmake and make via Docker
        echo -e "${BLUE}🛠 Running qmake via Docker...${NC}"

        # First, ensure we're starting with a clean build environment
        if [[ "$CLEAN" == true ]]; then
            echo -e "${BLUE}🧹 Cleaning up any existing build artifacts...${NC}"
            docker run --rm \
                -v "$PROJECT_DIR":/workspace \
                -w /workspace \
                ianlaue/qt-aarch64-builder:latest \
                bash -c "rm -rf /workspace/build/$TARGET-$BUILD_TYPE/* /workspace/*.o /workspace/moc_* /workspace/qrc_* /workspace/Makefile"
        fi

        # Create the build directory and run qmake
        # Use proper shadow build to avoid artifacts in top directory
        docker run --rm \
            -v "$PROJECT_DIR":/workspace \
            -w /workspace \
            ianlaue/qt-aarch64-builder:latest \
            bash -c "mkdir -p /workspace/build/$TARGET-$BUILD_TYPE && \
                   cd /workspace/build/$TARGET-$BUILD_TYPE && \
                   qmake /workspace/light_qt5.pro CONFIG+=$BUILD_TYPE"

        echo -e "${BLUE}🧱 Running make via Docker...${NC}"
        # Run make with the cross-compiler in the build directory
        docker run --rm \
            -v "$PROJECT_DIR":/workspace \
            -w /workspace/build/$TARGET-$BUILD_TYPE \
            ianlaue/qt-aarch64-builder:latest \
            make -j4

        # Go back to the project directory
        cd "$PROJECT_DIR"

        # Check for the binary in various possible locations
        if [[ -f "$BINARY" ]]; then
            echo -e "${GREEN}✅ Binary found at expected location: $BINARY${NC}"
        elif [[ -f "$PROJECT_DIR/$APP_NAME" ]]; then
            echo -e "${BLUE}📦 Copying build artifact from project root to expected location...${NC}"
            cp "$PROJECT_DIR/$APP_NAME" "$BINARY"
        elif [[ -f "$PROJECT_DIR/build/$APP_NAME" ]]; then
            echo -e "${BLUE}📦 Copying build artifact from build directory to expected location...${NC}"
            cp "$PROJECT_DIR/build/$APP_NAME" "$BINARY"
        else
            # Search for the binary
            echo -e "${YELLOW}🔍 Searching for build artifact...${NC}"
            FOUND_BINARY=$(find "$PROJECT_DIR" -name "$APP_NAME" -type f -executable -print -quit)

            if [[ -n "$FOUND_BINARY" ]]; then
                echo -e "${BLUE}📦 Copying found artifact to expected location...${NC}"
                cp "$FOUND_BINARY" "$BINARY"
            else
                echo -e "${RED}❌ Cannot find build artifact.${NC}"
                exit 1
            fi
        fi
    else
        echo -e "${RED}❌ Docker build environment not found.${NC}"
        echo -e "${YELLOW}Please ensure the qt_aarch64_builder directory exists with a Makefile.${NC}"
        exit 1
    fi

elif [[ "$TARGET" == "x86_64" ]]; then
    echo -e "${YELLOW}🏗️ Building for x86_64 local host with ${BUILD_TYPE} configuration...${NC}"
    # BUILD_DIR already set and created above
    cd "$BUILD_DIR"

    if [[ "$CLEAN" == true ]]; then
        echo -e "${BLUE}🧹 Cleaning previous build for x86_64-$BUILD_TYPE...${NC}"
        rm -rf ./*
    fi

    # The .pro file is located by the top-level logic, so this check is no longer needed here.

    echo -e "${BLUE}🛠 Running qmake...${NC}"
    # macOS-specific logic
    if [[ "$(uname)" == "Darwin" ]]; then
        # Try Homebrew Qt5, then official Qt install
        if [[ -x "/usr/local/opt/qt@5/bin/qmake" ]]; then
            QMAKE="/usr/local/opt/qt@5/bin/qmake"
        elif [[ -x "/opt/homebrew/opt/qt@5/bin/qmake" ]]; then
            QMAKE="/opt/homebrew/opt/qt@5/bin/qmake"
        elif [[ -x "/Applications/Qt/5.14.0/clang_64/bin/qmake" ]]; then
            QMAKE="/Applications/Qt/5.14.0/clang_64/bin/qmake"
        else
            QMAKE=$(command -v qmake || command -v qmake-qt5)
        fi
        if [[ -z "$QMAKE" ]]; then
            echo -e "${RED}❌ Error: Qt 5.14 qmake not found. Please install Qt 5.14 (brew install qt@5 or from qt.io).${NC}"
            exit 1
        fi
        echo "Using qmake: $($QMAKE --version | head -n 1)"
        $QMAKE "$PRO_FILE" -spec macx-clang CONFIG+=$BUILD_TYPE DEFINES+=DESKTOP_BUILD
        # Use sysctl for number of CPUs
        MAKE_JOBS=$(sysctl -n hw.ncpu)
    else
        QMAKE=$(command -v qmake || command -v qmake-qt5)
        if [[ -z "$QMAKE" ]]; then
            echo -e "${RED}❌ Error: qmake not found. Please install Qt development tools.${NC}"
            exit 1
        fi
        echo "Using qmake: $($QMAKE --version | head -n 1)"

        # Use nproc for number of CPUs
        MAKE_JOBS=$(nproc)

        # Build cutekeyboard submodule if not skipped
        build_cutekeyboard "$QMAKE" $MAKE_JOBS

        $QMAKE "$PRO_FILE" -spec linux-g++ CONFIG+=$BUILD_TYPE DEFINES+=DESKTOP_BUILD

    fi
    echo -e "${BLUE}🛠 Running make...${NC}"
    make -j$MAKE_JOBS
    BINARY="$BUILD_DIR/$APP_NAME"

    # Return to project root
    cd "$PROJECT_DIR"
fi

# Verify build was successful
if [[ -f "$BINARY" ]]; then
    echo -e "${GREEN}✅ Build succeeded: $BINARY${NC}"
    file "$BINARY"

    # Offer to run the application
    if [[ "$RUN_AFTER" == true ]]; then
        echo -e "${BLUE}▶️ Running $APP_NAME...${NC}"
        if [[ "$(uname)" == "Darwin" && -d "$APP_BUNDLE" ]]; then
            open "$APP_BUNDLE"
        else
            "$BINARY"
        fi
    elif [[ "$TARGET" == "x86_64" ]]; then
        echo -e "${YELLOW}📝 To run the application, use:${NC}"
        if [[ "$(uname)" == "Darwin" && -d "$APP_BUNDLE" ]]; then
            echo -e "${BLUE}open \"$APP_BUNDLE\"${NC}"
        else
            echo -e "${BLUE}\"$BINARY\"${NC}"
        fi
        echo -e "${YELLOW}Or use the run script:${NC}"
        echo -e "${BLUE}$SCRIPT_DIR/run.sh${NC}"
    fi
elif [[ "$(uname)" == "Darwin" ]]; then
    # Check for .app bundle output on macOS
    APP_BUNDLE="$BUILD_DIR/${APP_NAME}.app"
    MAC_BINARY="$APP_BUNDLE/Contents/MacOS/$APP_NAME"
    if [[ -f "$MAC_BINARY" ]]; then
        echo -e "${GREEN}✅ Build succeeded: $MAC_BINARY${NC}"
        file "$MAC_BINARY"
        echo -e "${YELLOW}📝 To run the application, use:${NC}"
        echo -e "${BLUE}open \"$APP_BUNDLE\"${NC}"
        echo -e "${YELLOW}Or use the run script:${NC}"
        echo -e "${BLUE}$SCRIPT_DIR/run.sh${NC}"
        exit 0
    fi
    echo -e "${RED}❌ Build failed: $MAC_BINARY not found${NC}"
    exit 1
else
    echo -e "${RED}❌ Build failed: $BINARY not found${NC}"
    exit 1
fi

# Set ini config
SRC_DIR="$(dirname "$0")/../config"
SRC_FILE="$SRC_DIR/ui.ini"
DEST_DIR="$HOME/.config/Prospr"
DEST_FILE="$DEST_DIR/Light.ini"

if [[ "$(uname)" == "Darwin" ]]; then
    if [ -f "$SRC_FILE" ]; then
        mkdir -p "$DEST_DIR"
        cp -v "$SRC_FILE" "$DEST_FILE"
        echo "[set_config.sh] Copied $SRC_FILE to $DEST_FILE"
    else
        echo "[set_config.sh] Source file not found: $SRC_FILE"
        exit 1
    fi
else
    echo "[set_config.sh] Not running on Darwin, skipping config copy."
fi


echo -e "${GREEN}✅ Build completed successfully${NC}"
