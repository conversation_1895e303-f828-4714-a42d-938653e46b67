#!/bin/bash

# Prospr Light: Linux Developer Environment Setup
# -------------------------------------------------
# This script checks for and installs all required Qt and QML dependencies
# for building and running the Prospr Light application on Ubuntu 22.04.x LTS only.
# It does NOT set any environment variables or application settings.
# All runtime and UI configuration is handled in main.qml and the application code.

if [[ "$(uname -s)" == "Linux" ]]; then
    # Install lsb-release, sudo, and tzdata (for non-interactive Docker builds)
    export DEBIAN_FRONTEND=noninteractive
    apt-get update
    apt-get install -y lsb-release sudo tzdata git
    ln -fs /usr/share/zoneinfo/Etc/UTC /etc/localtime
    dpkg-reconfigure --frontend noninteractive tzdata

    # Check platform
    if ! lsb_release -d | grep -q "Ubuntu 22.04"; then
        echo "Unsupported platform. Please use Ubuntu 22.04.x LTS for Prospr Light development."
        exit 1
    fi

    # List of all required Qt packages with explicit versions
    REQUIRED_PACKAGES=(
        "qtdeclarative5-dev=5.15.3+dfsg-1"
        "qtquickcontrols2-5-dev=5.15.3+dfsg-1"
        "qttools5-dev-tools=5.15.3-1"
        "qtdeclarative5-dev-tools=5.15.3+dfsg-1"
        "qml-module-qtquick2=5.15.3+dfsg-1"
        "qml-module-qtquick-controls2=5.15.3+dfsg-1"
        "qml-module-qtquick-window2=5.15.3+dfsg-1"
        "qml-module-qtquick-layouts=5.15.3+dfsg-1"
        "qml-module-qtgraphicaleffects=5.15.3-1"
        "qt5-qmltooling-plugins=5.15.3+dfsg-1"
        "build-essential=12.9ubuntu3"
        "qtvirtualkeyboard-plugin=5.15.3+dfsg-1"
        "qml-module-qt-labs-folderlistmodel=5.15.3+dfsg-1"
        "qtbase5-private-dev"           # for cutekeyboard
        "qtdeclarative5-private-dev"    # for cutekeyboard
    )

    sudo apt-get install -y "${REQUIRED_PACKAGES[@]}"

    # Set library and pkg-config paths for Linux Qt modules
    export LD_LIBRARY_PATH="/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH"
    export PKG_CONFIG_PATH="/usr/lib/x86_64-linux-gnu/pkgconfig:$PKG_CONFIG_PATH"
fi

if [[ "$(uname -s)" == "Darwin" ]]; then
    echo "Detected macOS. Setting up dependencies using Homebrew."
    # Check for Homebrew
    if ! command -v brew >/dev/null 2>&1; then
        echo "Homebrew not found. Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
        eval "$($(brew --prefix)/bin/brew shellenv)"
    fi

    # Install dependencies
    brew update
    brew install qt@5 cmake git
    echo "Qt5, cmake, and git installed via Homebrew."
    echo "Make sure /usr/local/opt/qt@5/bin is in your PATH if you need qmake or other tools."
    # Recommended environment variables for Qt5:
    echo "# To use Qt5 tools and pkg-config, consider adding the following to your shell profile:"
    echo "# export PATH=\"/usr/local/opt/qt@5/bin:$PATH\""
    echo "# export LDFLAGS=\"-L/usr/local/opt/qt@5/lib\""
    echo "# export CPPFLAGS=\"-I/usr/local/opt/qt@5/include\""
    echo "# export PKG_CONFIG_PATH=\"/usr/local/opt/qt@5/lib/pkgconfig\""

    echo "Building and installing cutekeyboard plugin..."
    pushd "$(dirname "$0")/../3rdparty/cutekeyboard/src"
    if command -v qmake >/dev/null 2>&1; then
        qmake || /usr/local/opt/qt@5/bin/qmake
    else
        /usr/local/opt/qt@5/bin/qmake
    fi
    make -j$(sysctl -n hw.logicalcpu)
    make install
    popd

else
    echo "Unsupported platform. Please use Ubuntu 22.04.x LTS or macOS for Prospr Light development."
    exit 1
fi
